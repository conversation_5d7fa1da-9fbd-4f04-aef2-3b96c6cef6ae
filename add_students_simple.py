#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import random
from datetime import datetime, timedelta

def add_students():
    """إضافة طلاب جدد مع درجاتهم"""
    
    # أسماء الطلاب
    students_data = [
        ("أحمد محمد الأحمد", "العلوم", "أحياء", "المستوى الأول"),
        ("فاطمة علي السعد", "الهندسة", "مدني", "المستوى الثاني"),
        ("محمد حسن الخالد", "الطب", "طب عام", "المستوى الثالث"),
        ("عائشة سعد الناصر", "الآداب", "عربي", "المستوى الأول"),
        ("علي أحمد الفهد", "إدارة الأعمال", "محاسبة", "المستوى الثاني"),
        ("مريم خالد السلطان", "التربية", "تربية ابتدائية", "المستوى الأول"),
        ("حسن محمد العبدالعزيز", "الحقوق", "قانون مدني", "المستوى الثالث"),
        ("زينب عبدالله اليوسف", "الصيدلة", "صيدلة إكلينيكية", "المستوى الثاني"),
        ("عبدالرحمن سلطان الإبراهيم", "العلوم", "كيمياء", "المستوى الأول"),
        ("سارة ناصر العمر", "الهندسة", "كهربائي", "المستوى الثالث"),
        ("خالد فهد الطارق", "الطب", "جراحة", "المستوى الثاني"),
        ("نورا حسين الزياد", "الآداب", "إنجليزي", "المستوى الأول"),
        ("سعد علي المشعل", "إدارة الأعمال", "تسويق", "المستوى الثالث"),
        ("هند محمد الوليد", "التربية", "تربية خاصة", "المستوى الثاني"),
        ("عبدالعزيز أحمد الصالح", "الحقوق", "قانون جنائي", "المستوى الأول"),
        ("ريم سعد العادل", "الصيدلة", "كيمياء صيدلانية", "المستوى الثالث"),
        ("يوسف خالد الماجد", "العلوم", "فيزياء", "المستوى الثاني"),
        ("لينا عبدالله الراشد", "الهندسة", "ميكانيكي", "المستوى الأول"),
        ("إبراهيم حسن التركي", "الطب", "باطنة", "المستوى الثالث"),
        ("أمل ناصر البندر", "الآداب", "تاريخ", "المستوى الثاني"),
        ("عمر فهد السلمان", "إدارة الأعمال", "إدارة", "المستوى الأول"),
        ("سلمى محمد الكريم", "التربية", "علم نفس", "المستوى الثالث"),
        ("طارق علي العبدالإله", "الحقوق", "قانون تجاري", "المستوى الثاني"),
        ("دانة أحمد المبارك", "الصيدلة", "علم الأدوية", "المستوى الأول"),
        ("زياد سعد الغامدي", "العلوم", "رياضيات", "المستوى الثالث"),
        ("غادة خالد القحطاني", "الهندسة", "حاسوب", "المستوى الثاني"),
        ("مشعل حسن الشهري", "الطب", "أطفال", "المستوى الأول"),
        ("منى عبدالله العتيبي", "الآداب", "جغرافيا", "المستوى الثالث"),
        ("وليد ناصر الحربي", "إدارة الأعمال", "اقتصاد", "المستوى الثاني"),
        ("رنا فهد المطيري", "التربية", "مناهج", "المستوى الأول"),
        ("ماجد محمد الدوسري", "الحقوق", "قانون دولي", "المستوى الثالث"),
        ("شهد علي الزهراني", "الصيدلة", "صيدلة صناعية", "المستوى الثاني"),
        ("صالح أحمد الشمري", "العلوم", "حاسوب", "المستوى الأول"),
        ("جود سعد العنزي", "الهندسة", "كيميائي", "المستوى الثالث"),
        ("عادل خالد الرشيد", "الطب", "نساء وولادة", "المستوى الثاني"),
        ("ملك حسن الفيصل", "الآداب", "فلسفة", "المستوى الأول"),
        ("راشد عبدالله الملك", "إدارة الأعمال", "مالية", "المستوى الثالث"),
        ("نور ناصر الأمير", "التربية", "إدارة تربوية", "المستوى الثاني"),
        ("تركي فهد الوزير", "الحقوق", "قانون إداري", "المستوى الأول"),
        ("بندر محمد الشيخ", "الصيدلة", "نباتات طبية", "المستوى الثالث"),
        ("سلمان علي الدكتور", "العلوم", "أحياء", "المستوى الثاني"),
        ("كريم أحمد المهندس", "الهندسة", "مدني", "المستوى الأول"),
        ("عبدالإله سعد الأستاذ", "الطب", "طب عام", "المستوى الثالث"),
        ("مبارك خالد الطبيب", "الآداب", "عربي", "المستوى الثاني"),
        ("عبدالمجيد حسن المحامي", "إدارة الأعمال", "محاسبة", "المستوى الأول"),
        ("حسين عبدالله القاضي", "التربية", "تربية ابتدائية", "المستوى الثالث"),
        ("عبدالله ناصر العالم", "الحقوق", "قانون مدني", "المستوى الثاني"),
        ("محمد فهد الباحث", "الصيدلة", "صيدلة إكلينيكية", "المستوى الأول"),
        ("أحمد محمد الكاتب", "العلوم", "كيمياء", "المستوى الثالث"),
        ("علي سعد الشاعر", "الهندسة", "كهربائي", "المستوى الثاني")
    ]
    
    conn = sqlite3.connect('students.db')
    cursor = conn.cursor()
    
    print("🚀 بدء إضافة الطلاب...")
    
    # الحصول على أعلى رقم طالب
    cursor.execute("SELECT MAX(CAST(student_id AS INTEGER)) FROM students WHERE student_id GLOB '[0-9]*'")
    result = cursor.fetchone()[0]
    max_id = result if result else 200000
    
    students_added = 0
    
    try:
        for i, (name, college, department, level) in enumerate(students_data):
            student_id = str(max_id + i + 1)
            
            # بيانات الطالب
            gender = random.choice(["ذكر", "أنثى"])
            nationality = random.choice(["سعودي", "مصري", "سوري", "أردني", "لبناني"])
            status = "نشط"
            enrollment_date = datetime.now() - timedelta(days=random.randint(365, 1095))
            birth_date = datetime.now() - timedelta(days=random.randint(6570, 9125))
            
            # إدراج الطالب
            cursor.execute('''
                INSERT INTO students (name, student_id, gender, nationality, college, department, 
                                    level, status, enrollment_date, birth_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, student_id, gender, nationality, college, department, 
                  level, status, enrollment_date.strftime('%Y-%m-%d'), birth_date.strftime('%Y-%m-%d')))
            
            new_student_id = cursor.lastrowid
            
            # إضافة درجات للطالب
            num_grades = random.randint(2, 4)
            
            for j in range(num_grades):
                year1 = random.randint(2022, 2025)
                year2 = year1 + 1
                semester_type = random.choice(["ربيع", "خريف"])
                semester = f"{semester_type} {year1}/{year2}"
                
                partial_grade = round(random.uniform(18, 35), 1)
                final_grade = round(random.uniform(30, 65), 1)
                total_grade = partial_grade + final_grade
                
                # تحديد التقدير
                if total_grade >= 90:
                    letter_grade = "A"
                elif total_grade >= 85:
                    letter_grade = "A-"
                elif total_grade >= 80:
                    letter_grade = "B+"
                elif total_grade >= 75:
                    letter_grade = "B"
                elif total_grade >= 71:
                    letter_grade = "B-"
                elif total_grade >= 68:
                    letter_grade = "C+"
                elif total_grade >= 65:
                    letter_grade = "C"
                elif total_grade >= 60:
                    letter_grade = "C-"
                elif total_grade >= 55:
                    letter_grade = "D+"
                elif total_grade >= 50:
                    letter_grade = "D"
                else:
                    letter_grade = "F"
                
                created_date = datetime.now() - timedelta(days=random.randint(1, 180))
                
                cursor.execute('''
                    INSERT INTO grades (student_id, semester, partial_grade, final_grade, 
                                      total_grade, letter_grade, created_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (new_student_id, semester, partial_grade, final_grade, 
                      total_grade, letter_grade, created_date.strftime('%Y-%m-%d %H:%M:%S')))
            
            students_added += 1
            if students_added % 10 == 0:
                print(f"✅ تم إضافة {students_added} طالب...")
        
        conn.commit()
        
        # إحصائيات نهائية
        cursor.execute('SELECT COUNT(*) FROM students')
        total_students = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) FROM grades')
        total_grades = cursor.fetchone()[0]
        
        print(f"\n🎉 تم إضافة {students_added} طالب بنجاح!")
        print(f"📊 إجمالي الطلاب: {total_students}")
        print(f"📊 إجمالي الدرجات: {total_grades}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    add_students()
