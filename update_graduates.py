import sqlite3

conn = sqlite3.connect('students.db')
cursor = conn.cursor()

# تحديث 10 طلاب ليصبحوا خريجين
cursor.execute('UPDATE students SET level = "الخريجين", status = "متخرج" WHERE id IN (SELECT id FROM students ORDER BY RANDOM() LIMIT 10)')
conn.commit()

# التحقق من النتيجة
cursor.execute('SELECT COUNT(*) FROM students WHERE level = "الخريجين"')
graduates_count = cursor.fetchone()[0]

cursor.execute('SELECT DISTINCT level FROM students ORDER BY level')
levels = [row[0] for row in cursor.fetchall()]

print(f'عدد الخريجين: {graduates_count}')
print(f'المستويات المتاحة: {levels}')

conn.close()
