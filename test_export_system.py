#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التصدير - نظام إدارة الطلاب
"""

import sys
import os
import sqlite3
from datetime import datetime

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect('students.db')
        cursor = conn.cursor()
        
        # التحقق من وجود جدول الطلاب
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='students'")
        students_table = cursor.fetchone()
        
        # التحقق من وجود جدول الدرجات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='grades'")
        grades_table = cursor.fetchone()
        
        conn.close()
        
        if students_table and grades_table:
            print("✅ قاعدة البيانات متصلة وتحتوي على الجداول المطلوبة")
            return True
        else:
            print("❌ قاعدة البيانات لا تحتوي على الجداول المطلوبة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def test_import_libraries():
    """اختبار استيراد المكتبات المطلوبة"""
    try:
        import pandas as pd
        print("✅ مكتبة pandas متوفرة")
        
        import openpyxl
        print("✅ مكتبة openpyxl متوفرة")
        
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate
        print("✅ مكتبة reportlab متوفرة")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        return False

def test_student_management_import():
    """اختبار استيراد نظام إدارة الطلاب"""
    try:
        import student_management
        print("✅ تم استيراد نظام إدارة الطلاب بنجاح")
        
        # التحقق من وجود دوال التصدير
        sms = student_management.StudentManagementSystem
        
        required_methods = [
            'get_all_students_data',
            'get_student_grades_data', 
            'get_all_grades_data',
            'export_students_to_excel',
            'export_student_grades_to_excel',
            'export_all_grades_to_excel',
            'export_students_to_pdf',
            'export_student_grades_to_pdf',
            'export_all_grades_to_pdf'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(sms, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ دوال مفقودة: {', '.join(missing_methods)}")
            return False
        else:
            print("✅ جميع دوال التصدير متوفرة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في استيراد نظام إدارة الطلاب: {e}")
        return False

def test_data_extraction():
    """اختبار استخراج البيانات"""
    try:
        # إنشاء مثيل وهمي للاختبار
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        import student_management
        app = student_management.StudentManagementSystem(root)
        
        # اختبار استخراج بيانات الطلاب
        students_data = app.get_all_students_data()
        print(f"✅ تم استخراج {len(students_data)} طالب")
        
        # اختبار استخراج جميع الدرجات
        grades_data = app.get_all_grades_data()
        print(f"✅ تم استخراج {len(grades_data)} درجة")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استخراج البيانات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام التصدير...")
    print("=" * 50)
    
    tests = [
        ("اختبار المكتبات", test_import_libraries),
        ("اختبار قاعدة البيانات", test_database_connection),
        ("اختبار استيراد النظام", test_student_management_import),
        ("اختبار استخراج البيانات", test_data_extraction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل في {test_name}")
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! نظام التصدير جاهز للاستخدام.")
        print("\n📋 الميزات المتاحة:")
        print("   📊 تصدير بيانات الطلاب إلى Excel")
        print("   📄 تصدير بيانات الطلاب إلى PDF")
        print("   📈 تصدير درجات طالب محدد إلى Excel")
        print("   📋 تصدير درجات طالب محدد إلى PDF")
        print("   📊 تصدير جميع الدرجات إلى Excel")
        print("   📄 تصدير جميع الدرجات إلى PDF")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
