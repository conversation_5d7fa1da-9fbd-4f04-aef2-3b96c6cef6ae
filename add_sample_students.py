#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import random
from datetime import datetime, timedelta

def add_sample_students():
    """إضافة 50 طالب مع درجاتهم ومستوياتهم"""
    
    # قائمة الأسماء العربية
    first_names = [
        "أحمد", "محمد", "علي", "حسن", "حسين", "عبدالله", "عبدالرحمن", "خالد", "سعد", "فهد",
        "عبدالعزيز", "سلطان", "ناصر", "عبدالمجيد", "يوسف", "إبراهيم", "عمر", "زياد", "طارق", "مشعل",
        "فاطمة", "عائشة", "خديجة", "مريم", "زينب", "سارة", "نورا", "هند", "ريم", "لينا",
        "أمل", "سلمى", "دانة", "غادة", "منى", "رنا", "شهد", "جود", "ملك", "نور",
        "عبدالإله", "بندر", "تركي", "راشد", "سلمان", "وليد", "ماجد", "صالح", "عادل", "كريم"
    ]
    
    middle_names = [
        "محمد", "أحمد", "علي", "حسن", "عبدالله", "سعد", "خالد", "عبدالرحمن", "ناصر", "فهد",
        "سلطان", "عبدالعزيز", "يوسف", "إبراهيم", "عمر", "طارق", "زياد", "مشعل", "عبدالمجيد", "وليد",
        "صالح", "عادل", "ماجد", "راشد", "تركي", "بندر", "سلمان", "كريم", "عبدالإله", "مبارك"
    ]
    
    last_names = [
        "الأحمد", "المحمد", "العلي", "الحسن", "الحسين", "السعد", "الخالد", "الناصر", "الفهد", "السلطان",
        "العبدالعزيز", "اليوسف", "الإبراهيم", "العمر", "الطارق", "الزياد", "المشعل", "الوليد", "الصالح", "العادل",
        "الماجد", "الراشد", "التركي", "البندر", "السلمان", "الكريم", "المبارك", "الغامدي", "القحطاني", "الشهري",
        "العتيبي", "الحربي", "المطيري", "الدوسري", "الزهراني", "الشمري", "العنزي", "الرشيد", "الفيصل", "الملك",
        "الأمير", "الوزير", "الشيخ", "الدكتور", "المهندس", "الأستاذ", "الطبيب", "المحامي", "القاضي", "العالم"
    ]
    
    colleges = ["العلوم", "الهندسة", "الطب", "الآداب", "إدارة الأعمال", "التربية", "الحقوق", "الصيدلة"]
    departments = {
        "العلوم": ["أحياء", "كيمياء", "فيزياء", "رياضيات", "حاسوب"],
        "الهندسة": ["مدني", "كهربائي", "ميكانيكي", "حاسوب", "كيميائي"],
        "الطب": ["طب عام", "جراحة", "باطنة", "أطفال", "نساء وولادة"],
        "الآداب": ["عربي", "إنجليزي", "تاريخ", "جغرافيا", "فلسفة"],
        "إدارة الأعمال": ["محاسبة", "تسويق", "إدارة", "اقتصاد", "مالية"],
        "التربية": ["تربية ابتدائية", "تربية خاصة", "علم نفس", "مناهج", "إدارة تربوية"],
        "الحقوق": ["قانون مدني", "قانون جنائي", "قانون تجاري", "قانون دولي", "قانون إداري"],
        "الصيدلة": ["صيدلة إكلينيكية", "كيمياء صيدلانية", "علم الأدوية", "صيدلة صناعية", "نباتات طبية"]
    }
    
    levels = ["المستوى الأول", "المستوى الثاني", "المستوى الثالث"]
    statuses = ["نشط", "متخرج", "منقطع", "محول"]
    genders = ["ذكر", "أنثى"]
    nationalities = ["سعودي", "مصري", "سوري", "أردني", "لبناني", "عراقي", "يمني", "سوداني", "مغربي", "تونسي"]
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('students.db')
    cursor = conn.cursor()
    
    # الحصول على أعلى رقم طالب موجود
    try:
        cursor.execute("SELECT MAX(CAST(student_id AS INTEGER)) FROM students WHERE student_id GLOB '[0-9]*'")
        max_id = cursor.fetchone()[0]
        if max_id is None:
            max_id = 200000
    except:
        max_id = 200000
    
    students_added = 0
    
    print("🚀 بدء إضافة 50 طالب جديد...")
    print("=" * 50)

    try:
        for i in range(50):
            # إنشاء اسم عشوائي
            first_name = random.choice(first_names)
            middle_name = random.choice(middle_names)
            last_name = random.choice(last_names)
            full_name = f"{first_name} {middle_name} {last_name}"
            
            # إنشاء رقم طالب
            student_id = str(max_id + i + 1)
            
            # اختيار كلية وقسم
            college = random.choice(colleges)
            department = random.choice(departments[college])
            
            # باقي البيانات
            level = random.choice(levels)
            status = random.choice(statuses)
            gender = random.choice(genders)
            nationality = random.choice(nationalities)
            
            # تواريخ عشوائية
            enrollment_date = datetime.now() - timedelta(days=random.randint(365, 1460))  # 1-4 سنوات
            birth_date = datetime.now() - timedelta(days=random.randint(6570, 10950))  # 18-30 سنة
            
            # إدراج الطالب
            cursor.execute('''
                INSERT INTO students (name, student_id, gender, nationality, college, department, 
                                    level, status, enrollment_date, birth_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (full_name, student_id, gender, nationality, college, department, 
                  level, status, enrollment_date.strftime('%Y-%m-%d'), birth_date.strftime('%Y-%m-%d')))
            
            # الحصول على ID الطالب المدرج
            new_student_id = cursor.lastrowid
            
            # إضافة درجات عشوائية للطالب (2-5 درجات)
            num_grades = random.randint(2, 5)
            
            for j in range(num_grades):
                # إنشاء فصل دراسي عشوائي
                year1 = random.randint(2020, 2025)
                year2 = year1 + 1
                semester_type = random.choice(["ربيع", "خريف"])
                semester = f"{semester_type} {year1}/{year2}"
                
                # درجات عشوائية
                partial_grade = round(random.uniform(15, 35), 1)
                final_grade = round(random.uniform(25, 65), 1)
                total_grade = partial_grade + final_grade
                
                # تحديد التقدير
                if total_grade >= 90:
                    letter_grade = "A"
                elif total_grade >= 85:
                    letter_grade = "A-"
                elif total_grade >= 80:
                    letter_grade = "B+"
                elif total_grade >= 75:
                    letter_grade = "B"
                elif total_grade >= 71:
                    letter_grade = "B-"
                elif total_grade >= 68:
                    letter_grade = "C+"
                elif total_grade >= 65:
                    letter_grade = "C"
                elif total_grade >= 60:
                    letter_grade = "C-"
                elif total_grade >= 55:
                    letter_grade = "D+"
                elif total_grade >= 50:
                    letter_grade = "D"
                else:
                    letter_grade = "F"
                
                # تاريخ إدخال عشوائي
                created_date = datetime.now() - timedelta(days=random.randint(1, 365))
                
                # إدراج الدرجة
                cursor.execute('''
                    INSERT INTO grades (student_id, semester, partial_grade, final_grade, 
                                      total_grade, letter_grade, created_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (new_student_id, semester, partial_grade, final_grade, 
                      total_grade, letter_grade, created_date.strftime('%Y-%m-%d %H:%M:%S')))
            
            students_added += 1
            print(f"تم إضافة الطالب {students_added}: {full_name} - {student_id}")
        
        # حفظ التغييرات
        conn.commit()
        print("=" * 50)
        print(f"✅ تم إضافة {students_added} طالب بنجاح مع درجاتهم!")
        print("=" * 50)

        # إحصائيات نهائية
        cursor.execute('SELECT COUNT(*) FROM students')
        total_students = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) FROM grades')
        total_grades = cursor.fetchone()[0]

        print(f"📊 إحصائيات قاعدة البيانات:")
        print(f"   • إجمالي الطلاب: {total_students}")
        print(f"   • إجمالي الدرجات: {total_grades}")
        print("=" * 50)

    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    add_sample_students()
