#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import random
from datetime import datetime, timedelta

def add_graduates():
    """إضافة طلاب خريجين"""
    
    # أسماء الطلاب الخريجين
    graduates_data = [
        ("د. أحمد محمد الخريج", "العلوم", "أحياء"),
        ("د. فاطمة علي المتخرجة", "الهندسة", "مدني"),
        ("د. محمد حسن الطبيب", "الطب", "طب عام"),
        ("د. عائشة سعد الأستاذة", "الآداب", "عربي"),
        ("د. علي أحمد المحاسب", "إدارة الأعمال", "محاسبة"),
        ("د. مريم خالد المعلمة", "التربية", "تربية ابتدائية"),
        ("د. حسن محمد المحامي", "الحقوق", "قانون مدني"),
        ("د. زينب عبدالله الصيدلانية", "الصيدلة", "صيدلة إكلينيكية"),
        ("د. عبدالرحمن سلطان الكيميائي", "العلوم", "كيمياء"),
        ("د. سارة ناصر المهندسة", "الهندسة", "كهربائي"),
        ("د. خالد فهد الجراح", "الطب", "جراحة"),
        ("د. نورا حسين الكاتبة", "الآداب", "إنجليزي"),
        ("د. سعد علي المسوق", "إدارة الأعمال", "تسويق"),
        ("د. هند محمد النفسية", "التربية", "علم نفس"),
        ("د. عبدالعزيز أحمد القاضي", "الحقوق", "قانون جنائي")
    ]
    
    conn = sqlite3.connect('students.db')
    cursor = conn.cursor()
    
    print("🎓 بدء إضافة الطلاب الخريجين...")
    
    # الحصول على أعلى رقم طالب
    cursor.execute("SELECT MAX(CAST(student_id AS INTEGER)) FROM students WHERE student_id GLOB '[0-9]*'")
    result = cursor.fetchone()[0]
    max_id = result if result else 300000
    
    graduates_added = 0
    
    try:
        for i, (name, college, department) in enumerate(graduates_data):
            student_id = str(max_id + i + 1)
            
            # بيانات الطالب الخريج
            gender = random.choice(["ذكر", "أنثى"])
            nationality = random.choice(["سعودي", "مصري", "سوري", "أردني", "لبناني"])
            status = "متخرج"
            level = "الخريجين"
            
            # تواريخ منطقية للخريجين (التحقوا قبل 4-6 سنوات)
            enrollment_date = datetime.now() - timedelta(days=random.randint(1460, 2190))  # 4-6 سنوات
            birth_date = datetime.now() - timedelta(days=random.randint(8030, 10950))  # 22-30 سنة
            
            # إدراج الطالب الخريج
            cursor.execute('''
                INSERT INTO students (name, student_id, gender, nationality, college, department, 
                                    level, status, enrollment_date, birth_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, student_id, gender, nationality, college, department, 
                  level, status, enrollment_date.strftime('%Y-%m-%d'), birth_date.strftime('%Y-%m-%d')))
            
            new_student_id = cursor.lastrowid
            
            # إضافة درجات للطالب الخريج (درجات عالية للخريجين)
            num_grades = random.randint(6, 8)  # خريجين لديهم درجات أكثر
            
            for j in range(num_grades):
                # سنوات دراسية سابقة
                year1 = random.randint(2019, 2023)
                year2 = year1 + 1
                semester_type = random.choice(["ربيع", "خريف"])
                semester = f"{semester_type} {year1}/{year2}"
                
                # درجات عالية للخريجين
                partial_grade = round(random.uniform(25, 35), 1)
                final_grade = round(random.uniform(45, 65), 1)
                total_grade = partial_grade + final_grade
                
                # تحديد التقدير (درجات أفضل للخريجين)
                if total_grade >= 90:
                    letter_grade = "A"
                elif total_grade >= 85:
                    letter_grade = "A-"
                elif total_grade >= 80:
                    letter_grade = "B+"
                elif total_grade >= 75:
                    letter_grade = "B"
                elif total_grade >= 71:
                    letter_grade = "B-"
                elif total_grade >= 68:
                    letter_grade = "C+"
                elif total_grade >= 65:
                    letter_grade = "C"
                elif total_grade >= 60:
                    letter_grade = "C-"
                elif total_grade >= 55:
                    letter_grade = "D+"
                elif total_grade >= 50:
                    letter_grade = "D"
                else:
                    letter_grade = "F"
                
                created_date = datetime.now() - timedelta(days=random.randint(365, 1095))
                
                cursor.execute('''
                    INSERT INTO grades (student_id, semester, partial_grade, final_grade, 
                                      total_grade, letter_grade, created_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (new_student_id, semester, partial_grade, final_grade, 
                      total_grade, letter_grade, created_date.strftime('%Y-%m-%d %H:%M:%S')))
            
            graduates_added += 1
            print(f"✅ تم إضافة الخريج {graduates_added}: {name} - {student_id}")
        
        conn.commit()
        
        # إحصائيات نهائية
        cursor.execute('SELECT COUNT(*) FROM students')
        total_students = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) FROM students WHERE level = "الخريجين"')
        total_graduates = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) FROM grades')
        total_grades = cursor.fetchone()[0]
        
        print(f"\n🎉 تم إضافة {graduates_added} خريج بنجاح!")
        print(f"📊 إجمالي الطلاب: {total_students}")
        print(f"🎓 إجمالي الخريجين: {total_graduates}")
        print(f"📊 إجمالي الدرجات: {total_grades}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    add_graduates()
