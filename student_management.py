import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
from datetime import datetime
import os
from PIL import Image, ImageTk
import hashlib
import shutil

# مكتبات التصدير
try:
    import pandas as pd
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils.dataframe import dataframe_to_rows
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("تحذير: مكتبات Excel غير متوفرة. يرجى تثبيت pandas و openpyxl")

try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_TABLE_ALIGNMENT
    WORD_AVAILABLE = True
    print("✅ مكتبات Word متوفرة ومحملة بنجاح")
except ImportError as e:
    WORD_AVAILABLE = False
    print(f"❌ تحذير: مكتبات Word غير متوفرة: {e}")
    print("يرجى تثبيت python-docx باستخدام: pip install python-docx")

class LoginSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("🎓 تسجيل الدخول - نظام إدارة الطلاب")
        self.root.configure(bg="#2c3e50")

        # توسيط النافذة من البداية
        self.center_window()

        # منع تغيير حجم النافذة
        self.root.resizable(False, False)

        # إنشاء قاعدة بيانات المستخدمين
        self.create_users_db()

        # إنشاء واجهة تسجيل الدخول
        self.create_login_interface()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        # تحديد الأبعاد أولاً - زيادة الارتفاع
        width = 500
        height = 700

        # حساب الموضع المركزي
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)

        # تطبيق الموضع والأبعاد
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_users_db(self):
        """إنشاء قاعدة بيانات المستخدمين مع مستخدم افتراضي"""
        conn = sqlite3.connect('users.db')
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'admin'
            )
        ''')

        # إضافة مستخدم افتراضي إذا لم يكن موجود
        default_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute("SELECT * FROM users WHERE username = 'admin'")
        if not cursor.fetchone():
            cursor.execute("""
                INSERT INTO users (username, password, full_name, role)
                VALUES ('admin', ?, 'المدير العام', 'admin')
            """, (default_password,))

        conn.commit()
        conn.close()

    def create_login_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg="#2c3e50")
        main_frame.pack(expand=True, fill='both', padx=40, pady=40)

        # منطقة الشعار
        logo_frame = tk.Frame(main_frame, bg="#2c3e50")
        logo_frame.pack(pady=(0, 30))

        # شعار النظام
        try:
            # محاولة تحميل الشعار المرفق
            if os.path.exists('logo.png'):
                logo_img = Image.open('logo.png')
                logo_img = logo_img.resize((120, 120), Image.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(logo_img)
                logo_label = tk.Label(logo_frame, image=self.logo_photo, bg="#2c3e50")
                logo_label.pack()
            else:
                # استخدام رمز نصي إذا لم توجد الصورة
                logo_label = tk.Label(logo_frame, text="🎓", font=("Arial", 80),
                                     bg="#2c3e50", fg="#3498db")
                logo_label.pack()
        except Exception as e:
            print(f"خطأ في تحميل الشعار: {e}")
            # استخدام رمز نصي كبديل
            logo_label = tk.Label(logo_frame, text="🎓", font=("Arial", 80),
                                 bg="#2c3e50", fg="#3498db")
            logo_label.pack()

        title_label = tk.Label(logo_frame, text="نظام إدارة الطلاب ",
                              font=("Cairo", 20, "bold"), bg="#2c3e50", fg="#ecf0f1")
        title_label.pack(pady=(10, 0))

        subtitle_label = tk.Label(logo_frame, text="تسجيل الدخول",
                                 font=("Cairo", 14), bg="#2c3e50", fg="#bdc3c7")
        subtitle_label.pack(pady=(5, 0))

        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg="#34495e", relief="raised", bd=2)
        login_frame.pack(pady=20, padx=20, fill='x')

        # عنوان تسجيل الدخول
        login_title = tk.Label(login_frame, text="🔐 تسجيل الدخول",
                              font=("Cairo", 16, "bold"), bg="#34495e", fg="#ecf0f1")
        login_title.pack(pady=(20, 30))

        # حقل اسم المستخدم
        username_frame = tk.Frame(login_frame, bg="#34495e")
        username_frame.pack(pady=15, padx=30, fill='x')

        tk.Label(username_frame, text="👤 اسم المستخدم:", font=("Cairo", 14, "bold"),
                bg="#34495e", fg="#ffffff").pack(anchor='e', pady=(0, 8))

        self.username_var = tk.StringVar()
        username_entry = tk.Entry(username_frame, textvariable=self.username_var,
                                 font=("Cairo", 12), relief="solid", bd=2,
                                 bg="#ffffff", fg="#2c3e50",
                                 highlightthickness=2, highlightcolor="#3498db",
                                 highlightbackground="#bdc3c7")
        username_entry.pack(fill='x', ipady=10)

        # حقل كلمة المرور
        password_frame = tk.Frame(login_frame, bg="#34495e")
        password_frame.pack(pady=15, padx=30, fill='x')

        tk.Label(password_frame, text="🔒 كلمة المرور:", font=("Cairo", 14, "bold"),
                bg="#34495e", fg="#ffffff").pack(anchor='e', pady=(0, 8))

        self.password_var = tk.StringVar()
        password_entry = tk.Entry(password_frame, textvariable=self.password_var,
                                 font=("Cairo", 12), show="*", relief="solid", bd=2,
                                 bg="#ffffff", fg="#2c3e50",
                                 highlightthickness=2, highlightcolor="#3498db",
                                 highlightbackground="#bdc3c7")
        password_entry.pack(fill='x', ipady=10)

        # أزرار تسجيل الدخول
        buttons_frame = tk.Frame(login_frame, bg="#34495e")
        buttons_frame.pack(pady=25, padx=30, fill='x')

        login_btn = tk.Button(buttons_frame, text="🚀 دخول", font=("Cairo", 12, "bold"),
                             bg="#27ae60", fg="white", relief="flat", bd=0,
                             command=self.login, cursor="hand2")
        login_btn.pack(fill='x', ipady=10, pady=(0, 10))

        # معلومات تسجيل الدخول الافتراضية
        info_frame = tk.Frame(main_frame, bg="#2c3e50")
        info_frame.pack(pady=25)

        info_label = tk.Label(info_frame, text="💡 بيانات تسجيل الدخول الافتراضية:",
                             font=("Cairo", 10, "bold"), bg="#2c3e50", fg="#f39c12")
        info_label.pack()

        default_info = tk.Label(info_frame, text="اسم المستخدم: admin | كلمة المرور: admin123",
                               font=("Cairo", 10), bg="#2c3e50", fg="#e74c3c")
        default_info.pack(pady=(5, 0))

        # ربط مفتاح Enter بتسجيل الدخول
        username_entry.bind('<Return>', lambda e: self.login())
        password_entry.bind('<Return>', lambda e: self.login())

        # التركيز على حقل اسم المستخدم
        username_entry.focus()

    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تشفير كلمة المرور
        hashed_password = hashlib.sha256(password.encode()).hexdigest()

        # التحقق من قاعدة البيانات
        conn = sqlite3.connect('users.db')
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE username = ? AND password = ?",
                      (username, hashed_password))
        user = cursor.fetchone()
        conn.close()

        if user:
            messagebox.showinfo("نجح تسجيل الدخول", f"مرحباً {user[3]}!")
            self.open_main_system()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_var.set("")  # مسح كلمة المرور

    def open_main_system(self):
        """فتح النظام الرئيسي"""
        self.root.destroy()
        main_root = tk.Tk()
        app = StudentManagementSystem(main_root)
        main_root.mainloop()

class StudentManagementSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة الطلاب")
        self.root.geometry("900x600")
        self.root.configure(bg="#f5f6fa")  # خلفية هادئة

        # تخصيص الأنماط
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TLabel', font=('Cairo', 13), background='#f5f6fa')
        style.configure('TButton', font=('Cairo', 12, 'bold'), foreground='#fff', background='#40739e', padding=8)
        style.map('TButton', background=[('active', '#273c75')])
        style.configure('Treeview', font=('Cairo', 12), rowheight=32, background='#fff', fieldbackground='#fff')
        style.configure('Treeview.Heading', font=('Cairo', 13, 'bold'), background='#40739e', foreground='#fff')
        # Set selection colors for better visibility
        style.map('Treeview',
                 background=[('selected', '#e74c3c')],  # Red background
                 foreground=[('selected', '#ffffff')])  # White text for contrast
        style.configure('TLabelframe', background='#f5f6fa', font=('Cairo', 14))
        style.configure('TLabelframe.Label', background='#f5f6fa', font=('Cairo', 14))
        style.configure('Modern.TLabelframe', background='#f5f6fa', font=('Cairo', 14))
        style.configure('Modern.TLabelframe.Label', background='#f5f6fa', font=('Cairo', 14))
        style.configure('TEntry', font=('Cairo', 12))

        # إنشاء قاعدة البيانات
        self.create_database()

        # تصحيح التواريخ غير الصحيحة
        self.fix_invalid_dates()

        # إنشاء النموذج
        self.create_form()

        # إنشاء جدول عرض البيانات
        self.create_treeview()

        # تحميل الطلاب مباشرة عند بدء التشغيل
        self.load_students()

        # إضافة اختصارات لوحة المفاتيح
        self.root.bind('<Control-r>', lambda e: self.clear_form())
        self.root.bind('<Control-R>', lambda e: self.clear_form())

    def create_database(self):
        # حذف قاعدة البيانات إذا كانت قديمة ولا تحتوي على الأعمدة الجديدة (للتحديث التلقائي)
        db_path = 'students.db'
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("PRAGMA table_info(students)")
                columns = [col[1] for col in cursor.fetchall()]
                needed = ['enrollment_date', 'birth_date', 'major', 'image_path', 'gender', 'nationality', 'college', 'department', 'status', 'level']
                needed.remove('major')
                if not all(col in columns for col in needed):
                    conn.close()
                    os.remove(db_path)
            except Exception:
                pass
        # إنشاء قاعدة البيانات
        conn = sqlite3.connect('students.db')
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                student_id TEXT UNIQUE NOT NULL,
                gender TEXT,
                nationality TEXT,
                college TEXT,
                department TEXT,
                status TEXT,
                image_path TEXT,
                enrollment_date TEXT,
                birth_date TEXT,
                level TEXT
            )
        ''')

        # إنشاء جدول الدرجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS grades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER,
                semester TEXT NOT NULL,
                partial_grade REAL NOT NULL,
                final_grade REAL NOT NULL,
                total_grade REAL NOT NULL,
                letter_grade TEXT NOT NULL,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE
            )
        ''')

        conn.commit()
        conn.close()

    def fix_invalid_dates(self):
        conn = sqlite3.connect('students.db')
        cursor = conn.cursor()
        cursor.execute("SELECT id, enrollment_date, birth_date FROM students")
        updates = []
        for row in cursor.fetchall():
            id_, enroll, birth = row
            def is_valid_date(date_str):
                try:
                    datetime.strptime(date_str, '%Y-%m-%d')
                    return True
                except Exception:
                    return False
            new_enroll = enroll
            new_birth = birth
            if enroll and not is_valid_date(enroll):
                new_enroll = ''
            if birth and not is_valid_date(birth):
                new_birth = ''
            if new_enroll != enroll or new_birth != birth:
                updates.append((new_enroll, new_birth, id_))
        for new_enroll, new_birth, id_ in updates:
            cursor.execute("UPDATE students SET enrollment_date=?, birth_date=? WHERE id=?", (new_enroll, new_birth, id_))
        conn.commit()
        conn.close()

    def create_form(self):
        form_frame = ttk.LabelFrame(self.root, text="بيانات الطالب", padding=20, style='Modern.TLabelframe')
        form_frame.pack(fill="x", padx=40, pady=15)

        # توزيع الحقول: الأعمدة من اليمين، كل حقل إدخال على اليمين والتسمية على اليسار في نفس الصف
        from datetime import date
        current_year = date.today().year
        years = [str(y) for y in range(current_year-40, current_year+1)]
        months = [str(m).zfill(2) for m in range(1, 13)]
        days = [str(d).zfill(2) for d in range(1, 32)]

        self.name_entry = ttk.Entry(form_frame, width=22)
        self.gender_var = tk.StringVar()
        self.gender_combo = ttk.Combobox(form_frame, textvariable=self.gender_var, values=["ذكر", "أنثى"], state='readonly', width=20)
        self.nationality_entry = ttk.Entry(form_frame, width=22)
        self.student_id_entry = ttk.Entry(form_frame, width=22)
        self.college_entry = ttk.Entry(form_frame, width=22)
        self.department_entry = ttk.Entry(form_frame, width=22)
        self.status_var = tk.StringVar()
        self.status_combo = ttk.Combobox(form_frame, textvariable=self.status_var, values=["منتظم", "موقوف", "خريج", "منسحب"], state='readonly', width=20)
        self.birth_date_entry = ttk.Entry(form_frame, width=14)
        self.enroll_date_entry = ttk.Entry(form_frame, width=14)
        self.image_path_var = tk.StringVar()
        self.image_entry = ttk.Entry(form_frame, textvariable=self.image_path_var, width=14, state='readonly')
        self.level_var = tk.StringVar()
        self.level_combo = ttk.Combobox(form_frame, textvariable=self.level_var, values=["المستوى الأول", "المستوى الثاني", "المستوى الثالث", "التخرج"], state='readonly', width=20)

        # إعداد الترقية التلقائية (افتراضياً مفعلة)
        self.auto_promotion_enabled = True

        # --- حقول التاريخ عبر قوائم منسدلة ---
        self.birth_year_var = tk.StringVar()
        self.birth_month_var = tk.StringVar()
        self.birth_day_var = tk.StringVar()
        self.enroll_year_var = tk.StringVar()
        self.enroll_month_var = tk.StringVar()
        self.enroll_day_var = tk.StringVar()
        # إعداد القيم
        from datetime import date
        current_year = date.today().year
        years = [str(y) for y in range(current_year-40, current_year+1)]
        months = [str(m).zfill(2) for m in range(1, 13)]
        days = [str(d).zfill(2) for d in range(1, 32)]
        # استبدال حقول الإدخال القديمة بقوائم منسدلة
        # تاريخ الميلاد
        birth_date_frame = ttk.Frame(form_frame)
        birth_date_frame.grid(row=1, column=2, padx=(2,8), pady=8, sticky='e')
        ttk.Combobox(birth_date_frame, textvariable=self.birth_year_var, values=years, width=5, state='readonly').pack(side=tk.LEFT)
        ttk.Label(birth_date_frame, text='-').pack(side=tk.LEFT)
        ttk.Combobox(birth_date_frame, textvariable=self.birth_month_var, values=months, width=3, state='readonly').pack(side=tk.LEFT)
        ttk.Label(birth_date_frame, text='-').pack(side=tk.LEFT)
        ttk.Combobox(birth_date_frame, textvariable=self.birth_day_var, values=days, width=5, state='readonly').pack(side=tk.LEFT)  # زيادة عرض اليوم
        # تاريخ الالتحاق
        enroll_date_frame = ttk.Frame(form_frame)
        ttk.Combobox(enroll_date_frame, textvariable=self.enroll_year_var, values=years, width=5, state='readonly').pack(side=tk.LEFT)
        ttk.Label(enroll_date_frame, text='-').pack(side=tk.LEFT)
        ttk.Combobox(enroll_date_frame, textvariable=self.enroll_month_var, values=months, width=3, state='readonly').pack(side=tk.LEFT)
        ttk.Label(enroll_date_frame, text='-').pack(side=tk.LEFT)
        ttk.Combobox(enroll_date_frame, textvariable=self.enroll_day_var, values=days, width=5, state='readonly').pack(side=tk.LEFT)
        # إخفاء حقول الإدخال النصية القديمة
        self.birth_date_entry.grid_remove()
        self.enroll_date_entry.grid_remove()

        # إنشاء النموذج
        fields = [
            [("👤الاسم:", self.name_entry), ("⚧ الجنس:", self.gender_combo), ("🌍الجنسية:", self.nationality_entry)],
            [("🆔الرقم القيد:", self.student_id_entry), ("🏛️الكلية:", self.college_entry), ("🎯القسم:", self.department_entry)],
            [("📊الوضع الدراسي:", self.status_combo), ("📈المستوى:", self.level_combo)],
            [("🎂تاريخ الميلاد:", birth_date_frame), ("📅تاريخ الالتحاق:", enroll_date_frame)]
        ]
        for col, col_fields in enumerate(fields):
            grid_col = 6 - (col * 2)  # 0=>6, 1=>4, 2=>2, 3=>0 (من اليمين)
            for row, (label, widget) in enumerate(col_fields):
                entry_col = grid_col
                label_col = grid_col + 1
                widget.grid(row=row, column=entry_col, padx=(2,8), pady=8, sticky='e')
                ttk.Label(form_frame, text=label, font=('Segoe UI', 11)).grid(row=row, column=label_col, padx=(2,8), pady=8, sticky='w')
        # قسم الصورة منفصل
        image_frame = ttk.Frame(form_frame)
        image_frame.grid(row=4, column=0, columnspan=8, padx=5, pady=15, sticky='ew')

        image_label = ttk.Label(image_frame, text="🖼️ الصورة:", font=('Segoe UI', 11))
        image_label.pack(side='right', padx=(0,10))

        choose_image_btn = ttk.Button(image_frame, text="📁 اختيار صورة",
                                     command=self.choose_image, style='Modern.TButton')
        choose_image_btn.pack(side='right', padx=5)

        # عرض مسار الصورة المختارة (للمطورين فقط - يمكن إخفاؤه)
        # self.image_entry.pack(side='right', padx=5)

        # ضبط توزيع الأعمدة
        for c in range(6):
            form_frame.grid_columnconfigure(c, weight=1)

        # أزرار التحكم
        btn_frame = ttk.Frame(self.root)
        btn_frame.pack(pady=10)
        tk.Button(btn_frame, text="➕ إضافة", command=self.add_student, font=("Cairo", 12, "bold"), bg="#27ae60", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2").pack(side=tk.LEFT, padx=10)
        tk.Button(btn_frame, text="✏️ تعديل", command=self.update_student, font=("Cairo", 12, "bold"), bg="#f39c12", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2").pack(side=tk.LEFT, padx=10)
        tk.Button(btn_frame, text="🗑️ حذف", command=self.delete_student, font=("Cairo", 12, "bold"), bg="#e74c3c", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2").pack(side=tk.LEFT, padx=10)
        clear_btn = tk.Button(btn_frame, text="🧹 مسح الحقول ", command=self.clear_form, font=("Cairo", 12, "bold"), bg="#6c5ce7", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
        clear_btn.pack(side=tk.LEFT, padx=10)
        # إضافة تأثير بصري عند التمرير
        clear_btn.bind("<Enter>", lambda e: clear_btn.config(bg="#5f3dc4"))
        clear_btn.bind("<Leave>", lambda e: clear_btn.config(bg="#6c5ce7"))

        # إعدادات الترقية التلقائية
        settings_frame = ttk.Frame(self.root)
        settings_frame.pack(pady=5)

        self.auto_promotion_var = tk.BooleanVar(value=True)
        auto_promotion_check = ttk.Checkbutton(
            settings_frame,
            text="🎓 الترقية التلقائية للطلاب (عند النجاح في فصل واحد)",
            variable=self.auto_promotion_var,
            command=self.toggle_auto_promotion
        )
        auto_promotion_check.pack(side=tk.LEFT, padx=10)

        # أزرار التصدير
        export_frame = ttk.Frame(self.root)
        export_frame.pack(pady=5)

        # زر تصدير الطلاب إلى Excel
        excel_students_btn = tk.Button(export_frame, text="📊 تصدير الطلاب Excel",
                                     command=self.export_students_to_excel,
                                     font=("Cairo", 11, "bold"), bg="#2ecc71", fg="#fff",
                                     padx=15, pady=5, relief="flat", cursor="hand2")
        excel_students_btn.pack(side=tk.LEFT, padx=5)

        # زر تصدير الطلاب إلى Word
        word_students_btn = tk.Button(export_frame, text="📄 تصدير الطلاب Word",
                                    command=self.show_export_students_options,
                                    font=("Cairo", 11, "bold"), bg="#2980b9", fg="#fff",
                                    padx=15, pady=5, relief="flat", cursor="hand2")
        word_students_btn.pack(side=tk.LEFT, padx=5)

        # زر تصدير جميع الدرجات إلى Excel
        excel_grades_btn = tk.Button(export_frame, text="📈 تصدير الدرجات Excel",
                                   command=self.export_all_grades_to_excel,
                                   font=("Cairo", 11, "bold"), bg="#3498db", fg="#fff",
                                   padx=15, pady=5, relief="flat", cursor="hand2")
        excel_grades_btn.pack(side=tk.LEFT, padx=5)

        # زر تصدير جميع الدرجات إلى Word
        word_grades_btn = tk.Button(export_frame, text="📋 تصدير الدرجات Word",
                                  command=self.export_all_grades_to_word,
                                  font=("Cairo", 11, "bold"), bg="#8e44ad", fg="#fff",
                                  padx=15, pady=5, relief="flat", cursor="hand2")
        word_grades_btn.pack(side=tk.LEFT, padx=5)

    def choose_image(self):
        from tkinter import filedialog
        import shutil

        file_path = filedialog.askopenfilename(
            title="اختر صورة الطالب",
            filetypes=[("Image Files", "*.png *.jpg *.jpeg *.bmp")]
        )
        if file_path:
            try:
                # إنشاء مجلد الصور إذا لم يكن موجوداً
                images_dir = "student_images"
                if not os.path.exists(images_dir):
                    os.makedirs(images_dir)

                # الحصول على اسم الملف والامتداد
                filename = os.path.basename(file_path)
                name, ext = os.path.splitext(filename)

                # إنشاء اسم فريد للملف
                counter = 1
                new_filename = filename
                while os.path.exists(os.path.join(images_dir, new_filename)):
                    new_filename = f"{name}_{counter}{ext}"
                    counter += 1

                # نسخ الصورة إلى مجلد المشروع
                new_path = os.path.join(images_dir, new_filename)
                shutil.copy2(file_path, new_path)

                # حفظ المسار النسبي
                self.image_path_var.set(new_path)
                messagebox.showinfo("نجاح", f"تم نسخ الصورة بنجاح إلى: {new_path}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في نسخ الصورة: {e}")
                # في حالة الفشل، استخدم المسار الأصلي
                self.image_path_var.set(file_path)

    def on_select(self, event):
        """ملء حقول الإدخال عند تحديد طالب في الجدول"""
        selected = self.tree.selection()
        if selected:
            try:
                values = self.tree.item(selected[0])['values']
                if values and len(values) >= 5:  # التأكد من وجود البيانات
                    student_name = values[3]  # اسم الطالب
                    student_id = values[4]    # معرف الطالب

                    # ملء النموذج ببيانات الطالب
                    self.fill_form_with_student_data(student_id)

                    # تحديث عنوان النافذة
                    self.root.title(f"نظام إدارة الطلاب - جاري تعديل: {student_name}")
            except Exception as e:
                print(f"خطأ في تحديد الطالب: {e}")
                # في حالة الخطأ، إعادة تعيين العنوان
                self.root.title("نظام إدارة الطلاب")

    def fill_form_with_student_data(self, student_id):
        """ملء النموذج ببيانات الطالب المحدد"""
        try:
            # مسح النموذج أولاً
            self.clear_form()

            # جلب جميع بيانات الطالب من قاعدة البيانات
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM students WHERE id=?", (student_id,))
            row = cursor.fetchone()
            conn.close()

            if row:
                # تعبئة الحقول في النموذج
                self.name_entry.insert(0, row[1] if len(row) > 1 else '')  # الاسم
                self.student_id_entry.insert(0, row[2] if len(row) > 2 else '')  # الرقم الجامعي
                self.gender_var.set(row[3] if len(row) > 3 else '')  # الجنس

                # الجنسية
                self.nationality_entry.delete(0, tk.END)
                self.nationality_entry.insert(0, row[4] if len(row) > 4 else '')

                # الكلية
                self.college_entry.delete(0, tk.END)
                self.college_entry.insert(0, row[5] if len(row) > 5 else '')

                # القسم
                self.department_entry.delete(0, tk.END)
                self.department_entry.insert(0, row[6] if len(row) > 6 else '')

                # الحالة الأكاديمية
                self.status_var.set(row[7] if len(row) > 7 else '')

                # مسار الصورة
                self.image_path_var.set(row[8] if len(row) > 8 and row[8] else '')

                # تاريخ الالتحاق
                if len(row) > 9 and row[9]:
                    try:
                        y, m, d = row[9].split('-')
                        self.enroll_year_var.set(y)
                        self.enroll_month_var.set(m.zfill(2))
                        self.enroll_day_var.set(d.zfill(2))
                    except Exception:
                        self.enroll_year_var.set('')
                        self.enroll_month_var.set('')
                        self.enroll_day_var.set('')

                # تاريخ الميلاد
                if len(row) > 10 and row[10]:
                    try:
                        y, m, d = row[10].split('-')
                        self.birth_year_var.set(y)
                        self.birth_month_var.set(m.zfill(2))
                        self.birth_day_var.set(d.zfill(2))
                    except Exception:
                        self.birth_year_var.set('')
                        self.birth_month_var.set('')
                        self.birth_day_var.set('')

                # المستوى الدراسي
                self.level_var.set(row[11] if len(row) > 11 else '')

        except Exception as e:
            print(f"خطأ في ملء النموذج: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات الطالب: {e}")

    def on_double_click(self, event):
        """فتح نافذة تفاصيل الطالب عند النقر المزدوج"""
        selected = self.tree.selection()
        if selected:
            values = self.tree.item(selected[0])['values']
            student_id = values[4]  # معرف الطالب

            # جلب بيانات الطالب من قاعدة البيانات
            try:
                conn = sqlite3.connect('students.db')
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM students WHERE id=?", (student_id,))
                row = cursor.fetchone()
                conn.close()

                if row:
                    self.show_student_details(row)

            except Exception as e:
                print(f"خطأ في فتح تفاصيل الطالب: {e}")
                messagebox.showerror("خطأ", f"حدث خطأ في فتح تفاصيل الطالب: {e}")

    def create_treeview(self):
        # إطار البحث
        search_frame = ttk.Frame(self.root)
        search_frame.pack(fill="x", padx=40, pady=(5, 0))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30, font=("Cairo", 12))
        search_entry.pack(side=tk.RIGHT, padx=(0, 8))
        ttk.Label(search_frame, text="بحث بالاسم أو الرقم الجامعي:", font=("Cairo", 12)).pack(side=tk.RIGHT)
        search_btn = ttk.Button(search_frame, text="بحث", command=self.search_students, style='TButton')
        search_btn.pack(side=tk.RIGHT, padx=8)
        showall_btn = ttk.Button(search_frame, text="إظهار الكل", command=self.load_students, style='TButton')
        showall_btn.pack(side=tk.RIGHT, padx=8)
        search_entry.bind('<Return>', lambda _: self.search_students())
        self.search_entry_widget = search_entry  # حفظ مرجع لحقل البحث للاستخدام لاحقاً
        tree_frame = ttk.Frame(self.root)
        tree_frame.pack(fill="both", expand=True, padx=40, pady=15)
        # الأعمدة: الرقم، الاسم، الرقم الجامعي، تاريخ الالتحاق، تاريخ الميلاد (من اليمين)
        columns = ("birth_date", "enrollment_date", "student_id", "name", "id")
        self.tree = ttk.Treeview(tree_frame, columns=columns, show="headings", style='Treeview')

        # تحسين مظهر الجدول مع حدود واضحة بين الصفوف
        style = ttk.Style()
        style.configure("Treeview",
                       background="#ffffff",
                       foreground="#2c3e50",
                       fieldbackground="#ffffff",
                       borderwidth=2,
                       relief="solid",
                       rowheight=32)
        style.configure("Treeview.Heading",
                       background="#34495e",
                       foreground="#ffffff",
                       font=("Cairo", 11, "bold"),
                       borderwidth=2,
                       relief="raised")
        style.map("Treeview",
                 background=[('selected', '#e74c3c')],
                 foreground=[('selected', '#ffffff')])

        # إضافة ألوان متناوبة للصفوف لمحاكاة الحدود
        self.tree.tag_configure('oddrow', background='#f8f9fa')
        self.tree.tag_configure('evenrow', background='#ffffff')

        self.tree.heading("birth_date", text="🎂 تاريخ الميلاد")
        self.tree.heading("enrollment_date", text="📅 تاريخ الالتحاق")
        self.tree.heading("student_id", text="🆔 الرقم الجامعي")
        self.tree.heading("name", text="👤 الاسم")
        self.tree.heading("id", text="🔢 الرقم")
        self.tree.column("birth_date", width=90, anchor='center')
        self.tree.column("enrollment_date", width=90, anchor='center')
        self.tree.column("student_id", width=80, anchor='center')
        self.tree.column("name", width=100, anchor='center')
        self.tree.column("id", width=40, anchor='center')
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        self.tree.pack(side=tk.LEFT, fill="both", expand=True)
        scrollbar.pack(side=tk.RIGHT, fill="y")
        # Bind selection event to fill form fields for editing (triggers immediately on selection)
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        # Bind double-click event to show student details
        self.tree.bind('<Double-1>', self.on_double_click)

    def is_valid_date_any(self, date_str):
        """يقبل YYYY أو YYYY-MM أو YYYY-MM-DD"""
        if not date_str or not isinstance(date_str, str):
            return False

        parts = date_str.strip().split('-')

        try:
            # التحقق من السنة (YYYY)
            if len(parts) == 1:
                year = int(parts[0])
                return len(parts[0]) == 4 and 1900 <= year <= 2100

            # التحقق من السنة والشهر (YYYY-MM)
            elif len(parts) == 2:
                year, month = int(parts[0]), int(parts[1])
                return (len(parts[0]) == 4 and 1900 <= year <= 2100 and
                       1 <= month <= 12)

            # التحقق من التاريخ الكامل (YYYY-MM-DD)
            elif len(parts) == 3:
                year, month, day = int(parts[0]), int(parts[1]), int(parts[2])
                if not (len(parts[0]) == 4 and 1900 <= year <= 2100 and
                       1 <= month <= 12 and 1 <= day <= 31):
                    return False

                # التحقق من صحة التاريخ
                try:
                    datetime.strptime(date_str, '%Y-%m-%d')
                    return True
                except ValueError:
                    return False

            return False

        except ValueError:
            return False

    def add_student(self):
        # جمع البيانات من النموذج
        name = self.name_entry.get().strip()
        student_id = self.student_id_entry.get().strip()
        gender = self.gender_var.get().strip()
        nationality = self.nationality_entry.get().strip()
        college = self.college_entry.get().strip()
        department = self.department_entry.get().strip()
        status = self.status_var.get().strip()
        image_path = self.image_path_var.get().strip() if self.image_path_var.get() else ''
        level = self.level_var.get().strip()

        # تكوين التواريخ من القوائم المنسدلة
        birth_date = f"{self.birth_year_var.get()}-{self.birth_month_var.get()}-{self.birth_day_var.get()}"
        enrollment_date = f"{self.enroll_year_var.get()}-{self.enroll_month_var.get()}-{self.enroll_day_var.get()}"

        # التحقق من صحة البيانات
        is_valid, error_message = self.validate_student_data(
            name, student_id, gender, nationality, college, department,
            status, level, birth_date, enrollment_date
        )

        if not is_valid:
            messagebox.showerror("خطأ", error_message)
            return

        # إكمال التواريخ الناقصة
        enrollment_date = self.fix_date(enrollment_date)
        birth_date = self.fix_date(birth_date)

        try:
            with sqlite3.connect('students.db') as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO students (name, student_id, gender, nationality, college, department, status, image_path, enrollment_date, birth_date, level)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (name, student_id, gender, nationality, college, department, status, image_path, enrollment_date, birth_date, level))
                conn.commit()
            messagebox.showinfo("نجاح", "تمت إضافة الطالب بنجاح")
            self.clear_form()
            self.load_students()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "الرقم الجامعي موجود مسبقاً")
        except sqlite3.OperationalError as e:
            if 'database is locked' in str(e):
                messagebox.showerror("خطأ", "قاعدة البيانات قيد الاستخدام. يرجى إعادة تشغيل البرنامج أو التأكد من عدم وجود نسخة أخرى تعمل.")
            else:
                messagebox.showerror("خطأ", f"حدث خطأ في قاعدة البيانات: {e}")
        except Exception as e:
            print(f"[خطأ غير متوقع]: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}")

    def fix_date(self, date_str):
        """إكمال التواريخ الناقصة"""
        parts = date_str.split('-')
        if len(parts) == 1:
            return f"{parts[0]}-01-01"
        elif len(parts) == 2:
            return f"{parts[0]}-{parts[1]}-01"
        elif len(parts) == 3:
            return f"{parts[0]}-{parts[1]}-{parts[2]}"
        return ''

    def validate_student_data(self, name, student_id, gender, nationality, college, department, status, level, birth_date, enrollment_date):
        """التحقق من صحة بيانات الطالب"""
        # التحقق من الحقول المطلوبة
        required_fields = [name, student_id, gender, nationality, college, department, status, level]
        if not all(required_fields):
            return False, "يرجى ملء جميع الحقول المطلوبة"

        # التحقق من طول الاسم
        if len(name) < 2:
            return False, "الاسم يجب أن يكون أكثر من حرفين"

        # التحقق من صحة الرقم الجامعي
        if not student_id.replace('-', '').replace('_', '').isalnum():
            return False, "الرقم الجامعي يجب أن يحتوي على أرقام أو أحرف فقط"

        if len(student_id) < 3:
            return False, "الرقم الجامعي قصير جداً"

        # التحقق من صحة التواريخ
        if not self.is_valid_date_any(enrollment_date):
            return False, "يرجى اختيار تاريخ التحاق صحيح"

        if not self.is_valid_date_any(birth_date):
            return False, "يرجى اختيار تاريخ ميلاد صحيح"

        return True, "البيانات صحيحة"

    def update_student(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "الرجاء اختيار طالب للتعديل")
            return

        # الحصول على معرف الطالب المحدد
        student_id = self.tree.item(selected[0])['values'][4]  # الحصول على معرف الطالب من العمود الخامس
        
        name = self.name_entry.get().strip()
        student_id_num = self.student_id_entry.get().strip()
        gender = self.gender_var.get().strip()
        nationality = self.nationality_entry.get().strip()
        college = self.college_entry.get().strip()
        department = self.department_entry.get().strip()
        status = self.status_var.get().strip()
        image_path = self.image_path_var.get().strip() if self.image_path_var.get() else ''
        # استخدم القيم من القوائم المنسدلة لتكوين التواريخ
        birth_date = f"{self.birth_year_var.get()}-{self.birth_month_var.get()}-{self.birth_day_var.get()}"
        enrollment_date = f"{self.enroll_year_var.get()}-{self.enroll_month_var.get()}-{self.enroll_day_var.get()}"
        level = self.level_var.get().strip()

        # التحقق من صحة البيانات
        is_valid, error_message = self.validate_student_data(
            name, student_id_num, gender, nationality, college, department,
            status, level, birth_date, enrollment_date
        )

        if not is_valid:
            messagebox.showerror("خطأ", error_message)
            return

        enrollment_date = self.fix_date(enrollment_date)
        birth_date = self.fix_date(birth_date)

        try:
            with sqlite3.connect('students.db') as conn:
                cursor = conn.cursor()
                # التحقق مما إذا كان الرقم الجامعي الجديد مستخدماً من قبل طالب آخر
                cursor.execute("SELECT id FROM students WHERE student_id=? AND id!=?", (student_id_num, student_id))
                if cursor.fetchone():
                    messagebox.showerror("خطأ", "الرقم الجامعي مستخدم مسبقاً")
                    return
                
                # تحديث بيانات الطالب
                cursor.execute("""
                    UPDATE students SET name=?, student_id=?, gender=?, nationality=?, college=?, department=?, status=?, image_path=?, enrollment_date=?, birth_date=?, level=? WHERE id=?
                """, (name, student_id_num, gender, nationality, college, department, status, image_path, enrollment_date, birth_date, level, student_id))
                conn.commit()
                
                # تحديث القيم في الجدول مباشرة
                self.tree.item(selected[0], values=(
                    birth_date,
                    enrollment_date,
                    student_id_num,
                    name,
                    student_id
                ))
                
            messagebox.showinfo("نجاح", "تم تحديث بيانات الطالب بنجاح")
            self.clear_form()
            # إعادة تحميل الطلاب للتأكد من تحديث الجدول
            self.load_students()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "حدث خطأ في تحديث البيانات. قد يكون الرقم الجامعي مستخدماً مسبقاً.")
        except sqlite3.OperationalError as e:
            if 'database is locked' in str(e):
                messagebox.showerror("خطأ", "قاعدة البيانات قيد الاستخدام. يرجى إعادة تشغيل البرنامج أو التأكد من عدم وجود نسخة أخرى تعمل.")
            else:
                messagebox.showerror("خطأ", f"حدث خطأ في قاعدة البيانات: {e}")
        except Exception as e:
            print(f"[خطأ غير متوقع]: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}")

    def delete_student(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "الرجاء اختيار طالب للحذف")
            return

        # الحصول على معرف الطالب الصحيح من العمود الخامس
        student_id = self.tree.item(selected[0])['values'][4]

        confirm = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف الطالب؟")
        if confirm:
            try:
                with sqlite3.connect('students.db') as conn:
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM students WHERE id=?", (student_id,))
                    conn.commit()
                self.clear_form()
                self.load_students()
                messagebox.showinfo("نجاح", "تم حذف الطالب بنجاح")
            except sqlite3.OperationalError as e:
                if 'database is locked' in str(e):
                    messagebox.showerror("خطأ", "قاعدة البيانات قيد الاستخدام. يرجى إعادة تشغيل البرنامج أو التأكد من عدم وجود نسخة أخرى تعمل.")
                else:
                    messagebox.showerror("خطأ", f"حدث خطأ في قاعدة البيانات: {e}")
            except Exception as e:
                print(f"[خطأ غير متوقع]: {e}")
                messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}")

    def show_student_details(self, values):
        # فك القيم حسب ترتيب قاعدة البيانات بشكل آمن
        id = values[0] if len(values) > 0 else ''
        # جلب جميع الحقول من قاعدة البيانات لضمان الترتيب الصحيح
        # Create a new window for student details
        details_win = tk.Toplevel(self.root)
        details_win.title("تفاصيل الطالب")
        details_win.geometry("900x600")
        details_win.configure(bg="#f5f6fa")
        # جعل النافذة تبقى في المقدمة
        details_win.transient(self.root)
        details_win.grab_set()
        details_win.focus_set()
        
        # Main frame with grid layout
        main_frame = ttk.Frame(details_win, padding=20)
        main_frame.pack(fill='both', expand=True)
        main_frame.grid_columnconfigure(0, weight=3)
        main_frame.grid_columnconfigure(1, weight=1)
        main_frame.grid_rowconfigure(0, weight=1)

        # Student info frame (left, يأخذ معظم الشاشة)
        info_frame = ttk.Frame(main_frame)
        info_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 30))

        # Student image frame (right, أعلى)
        img_frame = ttk.Frame(main_frame)
        img_frame.grid(row=0, column=1, sticky='n', pady=(0, 10))

        # Get student data from database
        conn = sqlite3.connect('students.db')
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM students WHERE id=?", (id,))
        db_row = cursor.fetchone()
        conn.close()
        
        # Format dates helper function
        def format_date(date_str):
            if not date_str:
                return "غير محدد"
            try:
                # محاولة تنسيقات تاريخ مختلفة
                for fmt in ('%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%d-%m-%Y'):
                    try:
                        date_obj = datetime.strptime(str(date_str).strip(), fmt)
                        return date_obj.strftime('%d/%m/%Y')
                    except ValueError:
                        continue
                return "غير محدد"
            except Exception as e:
                print(f"[خطأ في تنسيق التاريخ {date_str}]: {e}")
                return "غير محدد"
        
        if db_row:
            # Extract data from database row
            name = db_row[1] or 'غير معروف'
            student_id = db_row[2] or 'غير معروف'
            gender = db_row[3] or 'غير محدد'
            nationality = db_row[4] or 'غير محدد'
            college = db_row[5] or 'غير محدد'
            department = db_row[6] or 'غير محدد'
            status = db_row[7] or 'غير محدد'
            # تصحيح فهارس الأعمدة
            # الأعمدة في قاعدة البيانات: 0=id, 1=name, 2=student_id, 3=gender, 4=nationality,
            # 5=college, 6=department, 7=status, 8=image_path, 9=enrollment_date, 10=birth_date
            image_path = db_row[8] if len(db_row) > 8 and db_row[8] else None
            enrollment_date = format_date(db_row[9] if len(db_row) > 9 else '')
            birth_date = format_date(db_row[10] if len(db_row) > 10 else '')
            level = db_row[11] if len(db_row) > 11 else ''
            # تحويل المستوى للنص العربي المناسب
            level_map = {
                '1': 'المستوى الأول', 'first': 'المستوى الأول', 'المستوى الأول': 'المستوى الأول',
                '2': 'المستوى الثاني', 'second': 'المستوى الثاني', 'المستوى الثانية': 'المستوى الثانية',
                '3': 'المستوى الثالث', 'third': 'المستوى الثالث', 'المستوى الثالثة': 'المستوى الثالثة',
            }
            level_key = str(level).strip().lower()
            level = level_map.get(level_key, level if level else 'غير محدد')
            
            # Load and display student image if exists
            if image_path:
                try:
                    # تنظيف مسار الملف والتعامل مع المسافات والشرطات المائلة
                    clean_path = os.path.normpath(image_path.strip())
                    clean_path = clean_path.replace('\\', '/')
                    if os.path.exists(clean_path):
                        img = Image.open(clean_path)
                        # تصغير الصورة مع الحفاظ على التناسب (أقصى حجم 120x140)
                        img.thumbnail((120, 140), Image.LANCZOS)
                        # إضافة خلفية بيضاء إذا كانت الصورة أصغر من الإطار (اختياري)
                        bg = Image.new('RGB', (120, 140), (255, 255, 255))
                        img_w, img_h = img.size
                        offset = ((120 - img_w) // 2, (140 - img_h) // 2)
                        bg.paste(img, offset)
                        photo = ImageTk.PhotoImage(bg)
                        img_label = ttk.Label(img_frame, image=photo)
                        img_label.image = photo  # الاحتفاظ بالمرجع
                        img_label.pack(pady=10)
                        print(f"[DEBUG] تم تحميل الصورة بنجاح من: {clean_path}")
                    else:
                        print(f"[DEBUG] مسار الصورة غير موجود: {clean_path}")
                        ttk.Label(img_frame, text="مسار الصورة غير صحيح", 
                                font=("Cairo", 12), foreground="red").pack(pady=50)
                except Exception as e:
                    print(f"[ERROR] خطأ في تحميل الصورة: {e}")
                    print(f"[DEBUG] مسار الصورة: {image_path}")
                    ttk.Label(img_frame, text="خطأ في تحميل الصورة", 
                            font=("Cairo", 12), foreground="red").pack(pady=50)
            else:
                print("[DEBUG] لا يوجد مسار للصورة")
                ttk.Label(img_frame, text="لا توجد صورة متاحة", font=("Cairo", 12)).pack(pady=50)
        else:
            # If no data found in database, show error
            ttk.Label(details_win, text="لا توجد بيانات للطالب", 
                     font=("Cairo", 14, "bold")).pack(expand=True)
            close_btn = ttk.Button(details_win, text="إغلاق", 
                                 command=details_win.destroy)
            close_btn.pack(pady=20)
            return
        
        # Create student details
        details = [
            [("الاسم", name, "👤"), ("الجنس", gender, "♂️" if gender=="ذكر" else "♀️"), ("الجنسية", nationality, "🌍")],
            [("الرقم الجامعي", student_id, "#"), ("الكلية", college, "🏛️"), ("القسم", department, "📚")],
            [("الوضع الدراسي", status, "🎓"), ("تاريخ الميلاد", birth_date, "🎂"), ("تاريخ الالتحاق", enrollment_date, "📅"), ("المستوى", level, "⭐")],
        ]
        
        # Display details in a grid
        for col, col_fields in enumerate(details):
            grid_col = 4 - (col * 2)
            for row, (label, val, icon) in enumerate(col_fields):
                entry_col = grid_col
                label_col = grid_col + 1
                tk.Label(info_frame, text=val, bg="#fff", font=("Cairo", 13), fg="#222", anchor="e", justify="right").grid(row=row, column=entry_col, padx=(2,8), pady=8, sticky='e')
                tk.Label(info_frame, text=f"{icon} {label}", bg="#fff", font=("Cairo", 13, "bold"), fg="#752727", anchor="w").grid(row=row, column=label_col, padx=(2,8), pady=8, sticky='w')
        for c in range(6):
            info_frame.grid_columnconfigure(c, weight=1)

        # --- جدول الدرجات أسفل تفاصيل الطالب ---
        grades_frame = ttk.Frame(main_frame)
        grades_frame.grid(row=2, column=0, columnspan=2, sticky='ew', pady=(30, 0))
        grades_frame.grid_columnconfigure(0, weight=1)

        # رأس المستوى في منتصف الجدول
        level_label = tk.Label(grades_frame, text=f"المستوى: {level}", font=("Cairo", 15, "bold"), fg="#273c75", bg="#f5f6fa")
        level_label.pack(anchor='center', pady=(0, 10))

        # إنشاء جدول الدرجات مع عكس ترتيب الأعمدة وحدود محسنة
        columns = ("🏆 التقدير", "📊 المجموع", "📝 النهائي", "📋 الجزئي", "📅 الفصل الدراسي")

        # إضافة إطار مع حدود للجدول
        table_container = tk.Frame(grades_frame, bg="#2c3e50", relief="solid", bd=3)
        table_container.pack(fill='x', padx=10, pady=5)

        # تحسين مظهر جدول الدرجات مع حدود واضحة
        style = ttk.Style()

        # إنشاء الجدول أولاً بالنمط الافتراضي
        grades_table = ttk.Treeview(table_container, columns=columns, show="headings", height=5)
        # تخصيص مظهر جدول الدرجات
        grades_table.configure(style='Treeview')

        # تطبيق تحسينات خاصة بجدول الدرجات
        style.configure("Treeview",
                       background="#f8f9fa",
                       foreground="#2c3e50",
                       fieldbackground="#f8f9fa",
                       borderwidth=2,
                       relief="solid",
                       rowheight=35)
        style.configure("Treeview.Heading",
                       background="#2c3e50",
                       foreground="#ffffff",
                       font=("Cairo", 12, "bold"),
                       borderwidth=2,
                       relief="raised")
        style.map("Treeview",
                 background=[('selected', '#e74c3c')],
                 foreground=[('selected', '#ffffff')])

        # إضافة ألوان متناوبة للصفوف
        grades_table.tag_configure('grade_oddrow', background='#e8f4fd')
        grades_table.tag_configure('grade_evenrow', background='#f8f9fa')

        for col in columns:
            grades_table.heading(col, text=col, anchor='center')
            grades_table.column(col, anchor='center', width=100)

        grades_table.pack(fill='both', expand=True, padx=2, pady=2)

        # تحميل الدرجات المحفوظة من قاعدة البيانات
        saved_grades = self.load_grades_from_db(id)
        for index, grade_row in enumerate(saved_grades):
            semester, partial, final, total, letter_grade = grade_row
            tag = 'grade_evenrow' if index % 2 == 0 else 'grade_oddrow'
            grades_table.insert('', 'end', values=(letter_grade, total, final, partial, semester), tags=(tag,))

        # --- زر إضافة درجة ---
        def open_add_grade_window():
            add_win = tk.Toplevel(details_win)
            add_win.title("إضافة درجة")
            add_win.geometry("420x520")  # تكبير النافذة لعرض جميع الأزرار والحقول بوضوح
            add_win.configure(bg="#f5f6fa")
            # جعل نافذة إضافة الدرجة مرتبطة بنافذة تفاصيل الطالب
            add_win.transient(details_win)
            add_win.grab_set()
            add_win.focus_set()
            # الفصل الدراسي
            tk.Label(add_win, text="الفصل الدراسي:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(18,2))
            semester_var = tk.StringVar()
            semester_combo = ttk.Combobox(add_win, textvariable=semester_var, font=("Cairo", 12), state='readonly')
            semester_combo['values'] = ("ربيع", "خريف")
            semester_combo.pack(pady=4)
            # السنة الدراسية
            tk.Label(add_win, text="السنة الدراسية:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(10,2))
            year_from_var = tk.StringVar()
            year_to_var = tk.StringVar()
            from datetime import date
            current_year = date.today().year
            years = [str(y) for y in range(current_year-10, current_year+2)]
            year_frame = ttk.Frame(add_win)
            year_frame.pack(pady=4)
            year_from_combo = ttk.Combobox(year_frame, textvariable=year_from_var, font=("Cairo", 12), values=years, state='readonly', width=8)
            year_to_combo = ttk.Combobox(year_frame, textvariable=year_to_var, font=("Cairo", 12), values=years, state='readonly', width=8)
            year_from_combo.set(str(current_year))
            year_to_combo.set(str(current_year+1))
            year_from_combo.pack(side=tk.LEFT)
            tk.Label(year_frame, text=" \\", font=("Cairo", 12), bg="#f5f6fa").pack(side=tk.LEFT)
            year_to_combo.pack(side=tk.LEFT)
            # درجة الجزئي
            tk.Label(add_win, text="درجة الجزئي:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(10,2))
            partial_var = tk.StringVar()
            partial_entry = ttk.Entry(add_win, textvariable=partial_var, font=("Cairo", 12))
            partial_entry.pack(pady=4)
            # درجة النهائي
            tk.Label(add_win, text="درجة النهائي:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(10,2))
            final_var = tk.StringVar()
            final_entry = ttk.Entry(add_win, textvariable=final_var, font=("Cairo", 12))
            final_entry.pack(pady=4)
            # المجموع (يحسب تلقائي)
            tk.Label(add_win, text="المجموع:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(10,2))
            total_var = tk.StringVar()
            total_entry = ttk.Entry(add_win, textvariable=total_var, font=("Cairo", 12), state='readonly')
            total_entry.pack(pady=4)
            # التقدير (يحسب تلقائي)
            tk.Label(add_win, text="التقدير:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(10,2))
            grade_var = tk.StringVar()
            grade_entry = ttk.Entry(add_win, textvariable=grade_var, font=("Cairo", 12), state='readonly')
            grade_entry.pack(pady=4)
            # دالة حساب المجموع والتقدير
            def update_total_and_grade(*_):
                try:
                    p = float(partial_var.get()) if partial_var.get() else 0
                    f = float(final_var.get()) if final_var.get() else 0
                    total = p + f
                    total_var.set(str(total))
                    # تقدير تلقائي حسب النظام الجديد
                    grade_var.set(self.calculate_letter_grade(total))
                except Exception:
                    total_var.set("")
                    grade_var.set("")
            partial_var.trace_add('write', update_total_and_grade)
            final_var.trace_add('write', update_total_and_grade)
            # زر الحفظ
            def save_grade():
                semester = semester_var.get().strip()
                year_from = year_from_var.get().strip()
                year_to = year_to_var.get().strip()
                partial = partial_var.get().strip()
                final = final_var.get().strip()
                total = total_var.get().strip()
                grade = grade_var.get().strip()
                if not (semester and year_from and year_to and partial and final and total and grade):
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                    return
                year_display = f"{year_from}\\{year_to}"
                semester_display = f"{semester} {year_display}"

                # حفظ الدرجة في قاعدة البيانات
                if self.save_grade_to_db(id, semester_display, float(partial), float(final), float(total), grade):
                    # تطبيق ألوان متناوبة للدرجات
                    current_children = len(grades_table.get_children())
                    tag = 'grade_evenrow' if current_children % 2 == 0 else 'grade_oddrow'
                    grades_table.insert('', 'end', values=(grade, total, final, partial, semester_display), tags=(tag,))
                    messagebox.showinfo("نجح", "تم حفظ الدرجة بنجاح!")
                    add_win.destroy()
                    # إعادة التركيز لنافذة تفاصيل الطالب
                    details_win.lift()
                    details_win.focus_force()
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ الدرجة!")
            # زر الحفظ والإغلاق جنب بعض
            btns_frame = tk.Frame(add_win, bg="#f5f6fa")
            btns_frame.pack(pady=18)
            save_btn = tk.Button(btns_frame, text="حفظ", command=save_grade, font=("Cairo", 12, "bold"), bg="#27ae60", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
            save_btn.pack(side=tk.RIGHT, padx=8)
            close_btn = tk.Button(btns_frame, text="إغلاق", command=add_win.destroy, font=("Cairo", 12, "bold"), bg="#ee150d", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
            close_btn.pack(side=tk.RIGHT, padx=8)
        # دالة حذف الدرجة المحددة
        def delete_selected_grade():
            selected_item = grades_table.selection()
            if not selected_item:
                messagebox.showwarning("تحذير", "يرجى اختيار درجة لحذفها!")
                return

            # تأكيد الحذف
            result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الدرجة؟")
            if result:
                # الحصول على بيانات الدرجة المحددة
                item_values = grades_table.item(selected_item[0])['values']
                semester = item_values[4]  # الفصل الدراسي

                # حذف من قاعدة البيانات
                if self.delete_grade_from_db(id, semester):
                    # حذف من الجدول
                    grades_table.delete(selected_item[0])
                    messagebox.showinfo("نجح", "تم حذف الدرجة بنجاح!")
                    # إعادة تطبيق الألوان المتناوبة
                    self.refresh_grades_colors(grades_table)
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الدرجة!")

        # دالة تعديل الدرجة المحددة
        def edit_selected_grade():
            selected_item = grades_table.selection()
            if not selected_item:
                messagebox.showwarning("تحذير", "يرجى اختيار درجة لتعديلها!")
                return

            # الحصول على بيانات الدرجة المحددة
            item_values = grades_table.item(selected_item[0])['values']
            current_grade = item_values[0]  # التقدير
            current_total = item_values[1]  # المجموع
            current_final = item_values[2]  # النهائي
            current_partial = item_values[3]  # الجزئي
            current_semester = item_values[4]  # الفصل الدراسي

            # فتح نافذة التعديل
            self.open_edit_grade_window(details_win, grades_table, id, selected_item[0],
                                      current_semester, current_partial, current_final,
                                      current_total, current_grade)

        # زر "إضافة درجة" و"تعديل" و"حذف" و"إغلاق" جنب بعض أسفل جدول الدرجات
        btns_frame2 = tk.Frame(grades_frame, bg="#f5f6fa")
        btns_frame2.pack(pady=12)
        add_grade_btn = tk.Button(btns_frame2, text="➕ إضافة درجة", command=open_add_grade_window, font=("Cairo", 12, "bold"), bg="#27ae60", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
        add_grade_btn.pack(side=tk.RIGHT, padx=8)
        edit_grade_btn = tk.Button(btns_frame2, text="✏️ تعديل درجة", command=edit_selected_grade, font=("Cairo", 12, "bold"), bg="#f39c12", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
        edit_grade_btn.pack(side=tk.RIGHT, padx=8)
        delete_grade_btn = tk.Button(btns_frame2, text="🗑️ حذف درجة", command=delete_selected_grade, font=("Cairo", 12, "bold"), bg="#e74c3c", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
        delete_grade_btn.pack(side=tk.RIGHT, padx=8)

        # أزرار التصدير لدرجات الطالب المحدد
        export_excel_btn = tk.Button(btns_frame2, text="📊 تصدير Excel",
                                    command=lambda: self.export_student_grades_to_excel(id),
                                    font=("Cairo", 11, "bold"), bg="#2ecc71", fg="#fff",
                                    padx=15, pady=6, relief="flat", cursor="hand2")
        export_excel_btn.pack(side=tk.RIGHT, padx=8)

        export_word_btn = tk.Button(btns_frame2, text="📄 تصدير Word",
                                  command=lambda: self.export_student_grades_to_word(id),
                                  font=("Cairo", 11, "bold"), bg="#2980b9", fg="#fff",
                                  padx=15, pady=6, relief="flat", cursor="hand2")
        export_word_btn.pack(side=tk.RIGHT, padx=8)

        close_btn = tk.Button(btns_frame2, text="❌ إغلاق", command=details_win.destroy, font=("Cairo", 12, "bold"), bg="#ee150d", fg="#fff", relief="flat", padx=18, pady=6, cursor="hand2", activebackground="#273c75", bd=0, highlightthickness=0)
        close_btn.pack(side=tk.RIGHT, padx=8)

    def clear_form(self):
        """مسح جميع حقول النموذج"""
        # مسح حقول النص
        self.name_entry.delete(0, tk.END)
        self.student_id_entry.delete(0, tk.END)
        self.nationality_entry.delete(0, tk.END)
        self.college_entry.delete(0, tk.END)
        self.department_entry.delete(0, tk.END)

        # إعادة تعيين القوائم المنسدلة
        self.gender_var.set('')
        self.status_var.set('')
        self.level_var.set('')

        # مسح مسار الصورة
        self.image_path_var.set('')

        # إعادة تعيين تواريخ الميلاد
        self.birth_year_var.set('')
        self.birth_month_var.set('')
        self.birth_day_var.set('')

        # إعادة تعيين تواريخ التسجيل
        self.enroll_year_var.set('')
        self.enroll_month_var.set('')
        self.enroll_day_var.set('')

        # إعادة التركيز على الحقل الأول
        self.name_entry.focus_set()

        # إعادة تعيين عنوان النافذة
        self.root.title("نظام إدارة الطلاب")

        # رسالة تأكيد (اختيارية)
        # messagebox.showinfo("تم", "تم مسح جميع الحقول")

    def load_students(self):
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        # تحميل البيانات من قاعدة البيانات بدون الصورة وبالترتيب الجديد
        conn = sqlite3.connect('students.db')
        cursor = conn.cursor()
        cursor.execute("SELECT id, name, student_id, enrollment_date, birth_date FROM students")
        rows = cursor.fetchall()
        for index, row in enumerate(rows):
            birth_date = row[4]
            enrollment_date = row[3]
            # تحقق من أن التواريخ بصيغة صحيحة (YYYY-MM-DD)
            def is_valid_date(date_str):
                try:
                    datetime.strptime(date_str, '%Y-%m-%d')
                    return True
                except Exception:
                    return False
            # إذا كان التاريخ ناقص الشهر أو اليوم، نكمل بصفرين
            if birth_date:
                parts = birth_date.split('-')
                if len(parts) == 1:
                    birth_date = f"{parts[0]}-01-01"
                elif len(parts) == 2:
                    birth_date = f"{parts[0]}-{parts[1]}-01"
                elif not is_valid_date(birth_date):
                    birth_date = ''
            if enrollment_date:
                parts = enrollment_date.split('-')
                if len(parts) == 1:
                    enrollment_date = f"{parts[0]}-01-01"
                elif len(parts) == 2:
                    enrollment_date = f"{parts[0]}-{parts[1]}-01"
                elif not is_valid_date(enrollment_date):
                    enrollment_date = ''
            # تطبيق ألوان متناوبة للصفوف
            tag = 'evenrow' if index % 2 == 0 else 'oddrow'
            self.tree.insert("", tk.END, values=(birth_date, enrollment_date, row[2], row[1], row[0]), tags=(tag,))
        conn.close()

    def search_students(self):
        query = self.search_var.get().strip()
        self.search_entry_widget.focus_set()
        for item in self.tree.get_children():
            self.tree.delete(item)
        if not query:
            self.load_students()
            return

        # تحديد نوع البحث
        conn = sqlite3.connect('students.db')
        cursor = conn.cursor()

        # إذا كان النص المدخل رقماً، ابحث بدقة في الرقم الجامعي
        if query.isdigit():
            # بحث دقيق بالرقم الجامعي الكامل أولاً
            cursor.execute("""
                SELECT id, name, student_id, enrollment_date, birth_date FROM students
                WHERE student_id = ?
            """, (query,))
            results = cursor.fetchall()

            # إذا لم توجد نتائج دقيقة، ابحث بالأرقام التي تبدأ بالنص المدخل
            if not results:
                cursor.execute("""
                    SELECT id, name, student_id, enrollment_date, birth_date FROM students
                    WHERE student_id LIKE ?
                """, (f"{query}%",))
                results = cursor.fetchall()
        else:
            # بحث في الأسماء (غير حساس لحالة الأحرف)
            cursor.execute("""
                SELECT id, name, student_id, enrollment_date, birth_date FROM students
                WHERE LOWER(name) LIKE ?
            """, (f"%{query.lower()}%",))
            results = cursor.fetchall()
        if not results:
            self.tree.insert("", tk.END, values=("لا توجد نتائج", "", "", "", ""))
        else:
            for index, row in enumerate(results):
                birth_date = row[4]
                enrollment_date = row[3]
                def is_valid_date(date_str):
                    try:
                        datetime.strptime(date_str, '%Y-%m-%d')
                        return True
                    except Exception:
                        return False
                if birth_date:
                    parts = birth_date.split('-')
                    if len(parts) == 1:
                        birth_date = f"{parts[0]}-01-01"
                    elif len(parts) == 2:
                        birth_date = f"{parts[0]}-{parts[1]}-01"
                    elif not is_valid_date(birth_date):
                        birth_date = ''
                if enrollment_date:
                    parts = enrollment_date.split('-')
                    if len(parts) == 1:
                        enrollment_date = f"{parts[0]}-01-01"
                    elif len(parts) == 2:
                        enrollment_date = f"{parts[0]}-{parts[1]}-01"
                    elif not is_valid_date(enrollment_date):
                        enrollment_date = ''
                # تطبيق ألوان متناوبة للصفوف
                tag = 'evenrow' if index % 2 == 0 else 'oddrow'
                self.tree.insert("", tk.END, values=(birth_date, enrollment_date, row[2], row[1], row[0]), tags=(tag,))
        conn.close()

    def save_grade_to_db(self, student_id, semester, partial, final, total, grade):
        """حفظ الدرجة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()

            # التأكد من وجود جدول الدرجات فقط (بدون حذف)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS grades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER,
                    semester TEXT NOT NULL,
                    partial_grade REAL NOT NULL,
                    final_grade REAL NOT NULL,
                    total_grade REAL NOT NULL,
                    letter_grade TEXT NOT NULL,
                    created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE
                )
            ''')

            cursor.execute('''
                INSERT INTO grades (student_id, semester, partial_grade, final_grade, total_grade, letter_grade)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (student_id, semester, partial, final, total, grade))
            conn.commit()
            conn.close()
            print(f"تم حفظ الدرجة بنجاح للطالب {student_id}")

            # فحص إمكانية الترقية بعد إضافة الدرجة
            promotion_result = self.check_student_promotion(student_id)
            if promotion_result:
                # تحديث واجهة المستخدم إذا تمت الترقية
                self.load_students()

            return True
        except Exception as e:
            print(f"خطأ في حفظ الدرجة: {e}")
            import traceback
            traceback.print_exc()
            return False

    def load_grades_from_db(self, student_id):
        """تحميل الدرجات من قاعدة البيانات"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()

            # التأكد من وجود جدول الدرجات مع إضافة الأعمدة المفقودة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS grades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER,
                    semester TEXT NOT NULL,
                    partial_grade REAL NOT NULL,
                    final_grade REAL NOT NULL,
                    total_grade REAL NOT NULL,
                    letter_grade TEXT NOT NULL,
                    created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE
                )
            ''')

            cursor.execute('''
                SELECT semester, partial_grade, final_grade, total_grade, letter_grade
                FROM grades WHERE student_id = ?
                ORDER BY id DESC
            ''', (student_id,))
            grades = cursor.fetchall()
            conn.close()
            print(f"تم تحميل {len(grades)} درجة للطالب {student_id}")
            return grades
        except Exception as e:
            print(f"خطأ في تحميل الدرجات: {e}")
            import traceback
            traceback.print_exc()
            return []

    def delete_grade_from_db(self, student_id, semester):
        """حذف درجة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM grades
                WHERE student_id = ? AND semester = ?
            ''', (student_id, semester))
            conn.commit()
            conn.close()
            print(f"تم حذف الدرجة للطالب {student_id} في الفصل {semester}")
            return True
        except Exception as e:
            print(f"خطأ في حذف الدرجة: {e}")
            return False

    def calculate_letter_grade(self, total_score):
        """حساب التقدير الحرفي بناءً على المجموع الكلي"""
        if total_score >= 90:
            return "A"
        elif total_score >= 85:
            return "A-"
        elif total_score >= 80:
            return "B+"
        elif total_score >= 75:
            return "B"
        elif total_score >= 71:
            return "B-"
        elif total_score >= 68:
            return "C+"
        elif total_score >= 65:
            return "C"
        elif total_score >= 60:
            return "C-"
        elif total_score >= 55:
            return "D+"
        elif total_score >= 50:
            return "D"
        else:
            return "F"

    def set_rtl_paragraph(self, paragraph, text="", alignment=WD_ALIGN_PARAGRAPH.CENTER):
        """تعيين اتجاه النص في الفقرة من اليمين لليسار مع إصلاح انعكاس الكلمات"""
        try:
            from docx.oxml import OxmlElement
            from docx.oxml.ns import qn

            # مسح محتوى الفقرة إذا كان هناك نص جديد
            if text:
                for run in paragraph.runs:
                    run.text = ""

            # تطبيق المحاذاة
            paragraph.alignment = alignment

            # إضافة خاصية اتجاه النص من اليمين لليسار
            try:
                pPr = paragraph._p.get_or_add_pPr()
                bidi = OxmlElement('w:bidi')
                bidi.set(qn('w:val'), '1')
                pPr.append(bidi)
            except:
                pass

            # إضافة النص إذا تم توفيره
            if text:
                run = paragraph.add_run(text)
                run.font.name = 'Arial'

                # تطبيق اتجاه النص من اليمين لليسار على مستوى الـ run
                try:
                    rPr = run._r.get_or_add_rPr()
                    rtl = OxmlElement('w:rtl')
                    rtl.set(qn('w:val'), '1')
                    rPr.append(rtl)
                except:
                    pass

                return run

        except Exception as e:
            print(f"خطأ في تعيين اتجاه الفقرة: {e}")
            # في حالة الفشل، استخدم الطريقة البسيطة
            try:
                paragraph.alignment = alignment
            except:
                pass

    def set_rtl_table_cell(self, cell, text=""):
        """تعيين اتجاه النص في خلية الجدول من اليمين لليسار - إصلاح انعكاس الكلمات"""
        try:
            from docx.oxml import OxmlElement
            from docx.oxml.ns import qn

            # حفظ النص الأصلي إذا لم يتم توفير نص جديد
            if not text:
                text = cell.text

            # مسح محتوى الخلية
            cell._element.clear_content()

            # إنشاء فقرة جديدة
            paragraph = cell.add_paragraph()
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # إضافة خاصية اتجاه النص من اليمين لليسار على مستوى الفقرة
            try:
                pPr = paragraph._p.get_or_add_pPr()
                bidi = OxmlElement('w:bidi')
                bidi.set(qn('w:val'), '1')
                pPr.append(bidi)
            except:
                pass

            # إضافة النص مع تطبيق اتجاه النص من اليمين لليسار
            if text:
                run = paragraph.add_run(text)
                run.font.name = 'Arial'
                run.font.size = Pt(11)

                # تطبيق اتجاه النص من اليمين لليسار على مستوى الـ run
                try:
                    rPr = run._r.get_or_add_rPr()
                    rtl = OxmlElement('w:rtl')
                    rtl.set(qn('w:val'), '1')
                    rPr.append(rtl)
                except:
                    pass

                return run

        except Exception as e:
            print(f"خطأ في تعيين اتجاه الخلية: {e}")
            # في حالة الفشل، استخدم الطريقة البسيطة
            try:
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            except:
                pass

    def check_student_promotion(self, student_id):
        """فحص إمكانية ترقية الطالب للمستوى التالي"""
        # فحص إذا كانت الترقية التلقائية مفعلة
        if not self.auto_promotion_enabled:
            return False

        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()

            # الحصول على مستوى الطالب الحالي
            cursor.execute("SELECT level FROM students WHERE id = ?", (student_id,))
            student_level = cursor.fetchone()
            if not student_level:
                return False

            current_level = student_level[0]

            # تحديد المستوى التالي
            level_progression = {
                "المستوى الأول": "المستوى الثاني",
                "المستوى الثاني": "المستوى الثالث",
                "المستوى الثالث": "التخرج"
            }

            next_level = level_progression.get(current_level)
            if not next_level:
                return False

            # الحصول على درجات الطالب في الفصل الحالي
            cursor.execute("""
                SELECT semester, total_grade, letter_grade
                FROM grades
                WHERE student_id = ?
                ORDER BY created_date DESC
            """, (student_id,))

            grades = cursor.fetchall()
            conn.close()

            if not grades:
                return False

            # تحليل الدرجات لتحديد إذا كان الطالب مؤهل للترقية
            # فحص آخر درجة مضافة
            if grades:
                latest_grade = grades[0]  # أحدث درجة (مرتبة حسب التاريخ DESC)
                semester, total_grade, letter_grade = latest_grade

                # إذا كانت آخر درجة ناجحة (ليست F)، يمكن ترقيته
                if letter_grade != 'F':
                    return self.promote_student(student_id, next_level)

            return False

        except Exception as e:
            print(f"خطأ في فحص ترقية الطالب: {e}")
            return False

    def promote_student(self, student_id, new_level):
        """ترقية الطالب للمستوى التالي"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()

            # تحديث مستوى الطالب
            if new_level == "التخرج":
                cursor.execute("""
                    UPDATE students
                    SET level = ?, status = 'خريج'
                    WHERE id = ?
                """, (new_level, student_id))
            else:
                cursor.execute("""
                    UPDATE students
                    SET level = ?
                    WHERE id = ?
                """, (new_level, student_id))

            conn.commit()
            conn.close()

            # الحصول على اسم الطالب للإشعار
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM students WHERE id = ?", (student_id,))
            student_name = cursor.fetchone()[0]
            conn.close()

            # إظهار إشعار الترقية
            if new_level == "التخرج":
                messagebox.showinfo("🎓 تهانينا بالتخرج!",
                    f"🎉 الطالب {student_name} قد تخرج!\n"
                    f"✅ تم إكمال المستوى الثالث بنجاح\n"
                    f"🎓 تم تغيير مستواه إلى 'التخرج' وحالته إلى 'خريج' تلقائياً")
            else:
                messagebox.showinfo("🎉 ترقية تلقائية!",
                    f"🎯 تم ترقية الطالب {student_name} إلى {new_level}!\n"
                    f"✅ نجح في الفصل الدراسي\n"
                    f"🚀 انتقل للمستوى التالي تلقائياً")

            return True

        except Exception as e:
            print(f"خطأ في ترقية الطالب: {e}")
            return False

    def toggle_auto_promotion(self):
        """تبديل حالة الترقية التلقائية"""
        self.auto_promotion_enabled = self.auto_promotion_var.get()
        status = "مفعلة" if self.auto_promotion_enabled else "معطلة"
        print(f"الترقية التلقائية الآن: {status}")

        # إظهار رسالة تأكيد
        if self.auto_promotion_enabled:
            messagebox.showinfo("✅ تم التفعيل",
                "تم تفعيل الترقية التلقائية!\n"
                "سيتم ترقية الطلاب تلقائياً عند النجاح في أي فصل دراسي.\n"
                "المستوى الثالث ← التخرج مباشرة")
        else:
            messagebox.showinfo("⏸️ تم التعطيل",
                "تم تعطيل الترقية التلقائية.\n"
                "لن يتم ترقية الطلاب تلقائياً.")

    def refresh_grades_colors(self, grades_table):
        """إعادة تطبيق الألوان المتناوبة على جدول الدرجات"""
        for index, item in enumerate(grades_table.get_children()):
            tag = 'grade_evenrow' if index % 2 == 0 else 'grade_oddrow'
            grades_table.item(item, tags=(tag,))

    def open_edit_grade_window(self, parent_win, grades_table, student_id, selected_item,
                              current_semester, current_partial, current_final,
                              current_total, current_grade):
        """فتح نافذة تعديل الدرجة"""
        edit_win = tk.Toplevel(parent_win)
        edit_win.title("تعديل درجة")
        edit_win.geometry("420x520")
        edit_win.configure(bg="#f5f6fa")
        edit_win.transient(parent_win)
        edit_win.grab_set()
        edit_win.focus_set()

        # الفصل الدراسي
        tk.Label(edit_win, text="الفصل الدراسي:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(18,2))
        semester_frame = tk.Frame(edit_win, bg="#f5f6fa")
        semester_frame.pack(pady=5)

        semester_var = tk.StringVar()
        semester_combo = ttk.Combobox(semester_frame, textvariable=semester_var, values=["ربيع", "خريف"], state="readonly", width=15)
        semester_combo.pack(side=tk.LEFT, padx=5)

        # استخراج الفصل من النص الحالي
        if "الأول" in current_semester or "ربيع" in current_semester:
            semester_var.set("ربيع")
        elif "الثاني" in current_semester or "خريف" in current_semester:
            semester_var.set("خريف")

        # السنة الدراسية
        tk.Label(semester_frame, text="السنة:", font=("Cairo", 10), bg="#f5f6fa").pack(side=tk.LEFT, padx=(10,2))
        year_from_var = tk.StringVar()
        year_to_var = tk.StringVar()

        # استخراج السنة من النص الحالي
        if "\\" in current_semester:
            year_part = current_semester.split()[-1]  # آخر جزء
            if "\\" in year_part:
                years = year_part.split("\\")
                year_from_var.set(years[0])
                year_to_var.set(years[1])

        year_from_entry = tk.Entry(semester_frame, textvariable=year_from_var, width=6, font=("Cairo", 10))
        year_from_entry.pack(side=tk.LEFT, padx=2)
        tk.Label(semester_frame, text="/", font=("Cairo", 10), bg="#f5f6fa").pack(side=tk.LEFT)
        year_to_entry = tk.Entry(semester_frame, textvariable=year_to_var, width=6, font=("Cairo", 10))
        year_to_entry.pack(side=tk.LEFT, padx=2)

        # درجة الجزئي
        tk.Label(edit_win, text="درجة الجزئي:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(15,2))
        partial_var = tk.StringVar(value=str(current_partial))
        partial_entry = tk.Entry(edit_win, textvariable=partial_var, width=20, font=("Cairo", 12))
        partial_entry.pack(pady=5)

        # درجة النهائي
        tk.Label(edit_win, text="درجة النهائي:", font=("Cairo", 12), bg="#f5f6fa").pack(pady=(15,2))
        final_var = tk.StringVar(value=str(current_final))
        final_entry = tk.Entry(edit_win, textvariable=final_var, width=20, font=("Cairo", 12))
        final_entry.pack(pady=5)

        # دالة حفظ التعديل
        def save_edit():
            semester = semester_var.get()
            year_from = year_from_var.get()
            year_to = year_to_var.get()
            partial = partial_var.get()
            final = final_var.get()

            if not all([semester, year_from, year_to, partial, final]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول!")
                return

            try:
                partial = float(partial)
                final = float(final)
                total = partial + final

                # تحديد التقدير حسب النظام الجديد
                grade = self.calculate_letter_grade(total)

                year_display = f"{year_from}\\{year_to}"
                new_semester_display = f"{semester} {year_display}"

                # تحديث في قاعدة البيانات
                if self.update_grade_in_db(student_id, current_semester, new_semester_display, partial, final, total, grade):
                    # تحديث في الجدول
                    grades_table.item(selected_item, values=(grade, total, final, partial, new_semester_display))
                    messagebox.showinfo("نجح", "تم تعديل الدرجة بنجاح!")
                    edit_win.destroy()
                    parent_win.lift()
                    parent_win.focus_force()
                else:
                    messagebox.showerror("خطأ", "فشل في تعديل الدرجة!")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للدرجات!")

        # أزرار الحفظ والإغلاق
        btns_frame = tk.Frame(edit_win, bg="#f5f6fa")
        btns_frame.pack(pady=18)
        save_btn = tk.Button(btns_frame, text="💾 حفظ التعديل", command=save_edit, font=("Cairo", 12, "bold"), bg="#27ae60", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
        save_btn.pack(side=tk.RIGHT, padx=8)
        close_btn = tk.Button(btns_frame, text="❌ إغلاق", command=edit_win.destroy, font=("Cairo", 12, "bold"), bg="#ee150d", fg="#fff", padx=18, pady=6, relief="flat", cursor="hand2")
        close_btn.pack(side=tk.RIGHT, padx=8)

    def update_grade_in_db(self, student_id, old_semester, new_semester, partial, final, total, grade):
        """تحديث درجة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE grades
                SET semester = ?, partial_grade = ?, final_grade = ?, total_grade = ?, letter_grade = ?
                WHERE student_id = ? AND semester = ?
            ''', (new_semester, partial, final, total, grade, student_id, old_semester))
            conn.commit()
            conn.close()
            print(f"تم تحديث الدرجة للطالب {student_id}")
            return True
        except Exception as e:
            print(f"خطأ في تحديث الدرجة: {e}")
            return False

    # ==================== دوال التصدير ====================

    def get_all_students_data(self):
        """استخراج جميع بيانات الطلاب من قاعدة البيانات"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name, student_id, gender, nationality, college,
                       department, status, enrollment_date, birth_date, level
                FROM students
                ORDER BY name
            ''')
            students = cursor.fetchall()
            conn.close()

            # تحويل البيانات إلى قائمة من القواميس (بترتيب من اليمين لليسار)
            students_data = []
            for student in students:
                student_dict = {
                    'المستوى': student[10] or '',
                    'تاريخ الميلاد': student[9] or '',
                    'تاريخ التسجيل': student[8] or '',
                    'الحالة': student[7] or '',
                    'القسم': student[6] or '',
                    'الكلية': student[5] or '',
                    'الجنسية': student[4] or '',
                    'الجنس': student[3] or '',
                    'الرقم الجامعي': student[2] or '',
                    'الاسم': student[1] or '',
                    'الرقم': student[0]
                }
                students_data.append(student_dict)

            return students_data
        except Exception as e:
            print(f"خطأ في استخراج بيانات الطلاب: {e}")
            return []

    def get_student_grades_data(self, student_id):
        """استخراج درجات طالب محدد"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()

            # جلب بيانات الطالب
            cursor.execute('SELECT name, student_id FROM students WHERE id = ?', (student_id,))
            student_info = cursor.fetchone()

            if not student_info:
                return None

            # جلب درجات الطالب
            cursor.execute('''
                SELECT semester, partial_grade, final_grade, total_grade, letter_grade, created_date
                FROM grades WHERE student_id = ?
                ORDER BY created_date DESC
            ''', (student_id,))
            grades = cursor.fetchall()
            conn.close()

            # تحويل البيانات إلى قائمة من القواميس (بترتيب من اليمين لليسار)
            grades_data = []
            academic_years = set()  # لحساب السنوات الأكاديمية التي مر بها

            for grade in grades:
                grade_dict = {
                    'تاريخ الإدخال': grade[5],
                    'التقدير': grade[4],
                    'المجموع': grade[3],
                    'درجة النهائي': grade[2],
                    'درجة الجزئي': grade[1],
                    'الفصل الدراسي': grade[0]
                }
                grades_data.append(grade_dict)

                # استخراج السنة الأكاديمية من الفصل الدراسي
                semester = grade[0]
                if 'ربيع' in semester or 'خريف' in semester:
                    # استخراج السنة الأكاديمية (مثل 2025/2026)
                    if '\\' in semester:
                        year_part = semester.split('\\')[1] if len(semester.split('\\')) > 1 else semester.split('\\')[0]
                    elif '/' in semester:
                        year_part = semester.split('/')[1] if len(semester.split('/')) > 1 else semester.split('/')[0]
                    else:
                        year_part = "2025/2026"  # سنة افتراضية
                    academic_years.add(year_part)

            # حساب عدد المستويات بناءً على عدد السنوات الأكاديمية
            # كل سنة أكاديمية = مستوى واحد
            levels_count = len(academic_years)

            return {
                'student_name': student_info[0],
                'student_id': student_info[1],
                'grades': grades_data,
                'levels_count': levels_count  # عدد المستويات التي مر بها
            }
        except Exception as e:
            print(f"خطأ في استخراج درجات الطالب: {e}")
            return None

    def get_all_grades_data(self):
        """استخراج جميع الدرجات مع بيانات الطلاب"""
        try:
            conn = sqlite3.connect('students.db')
            cursor = conn.cursor()
            cursor.execute('''
                SELECT s.name, s.student_id, s.college, s.department, s.level,
                       g.semester, g.partial_grade, g.final_grade, g.total_grade,
                       g.letter_grade, g.created_date
                FROM students s
                JOIN grades g ON s.id = g.student_id
                ORDER BY s.name, g.created_date DESC
            ''')
            all_grades = cursor.fetchall()
            conn.close()

            # تحويل البيانات إلى قائمة من القواميس (بترتيب من اليمين لليسار)
            grades_data = []
            for grade in all_grades:
                grade_dict = {
                    'تاريخ الإدخال': grade[10],
                    'التقدير': grade[9],
                    'المجموع': grade[8],
                    'درجة النهائي': grade[7],
                    'درجة الجزئي': grade[6],
                    'الفصل الدراسي': grade[5],
                    'المستوى': grade[4],
                    'القسم': grade[3],
                    'الكلية': grade[2],
                    'الرقم الجامعي': grade[1],
                    'اسم الطالب': grade[0]
                }
                grades_data.append(grade_dict)

            return grades_data
        except Exception as e:
            print(f"خطأ في استخراج جميع الدرجات: {e}")
            return []

    def export_students_to_excel(self):
        """تصدير بيانات الطلاب إلى Excel"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبات Excel غير متوفرة\nيرجى تثبيت pandas و openpyxl")
            return

        try:
            # الحصول على بيانات الطلاب
            students_data = self.get_all_students_data()
            if not students_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات طلاب للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ ملف Excel - بيانات الطلاب"
            )

            if not file_path:
                return

            # إنشاء DataFrame
            df = pd.DataFrame(students_data)

            # إنشاء ملف Excel مع تنسيق
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='بيانات الطلاب', index=False)

                # تنسيق الملف
                worksheet = writer.sheets['بيانات الطلاب']

                # تنسيق الرأس
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_alignment = Alignment(horizontal="center", vertical="center")

                # تطبيق التنسيق على الرأس
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment

                # تنسيق البيانات
                data_alignment = Alignment(horizontal="center", vertical="center")
                thin_border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # تطبيق التنسيق على جميع الخلايا
                for row in worksheet.iter_rows():
                    for cell in row:
                        cell.alignment = data_alignment
                        cell.border = thin_border

                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 30)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            messagebox.showinfo("نجح التصدير", f"تم تصدير بيانات {len(students_data)} طالب بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
            print(f"خطأ في تصدير Excel: {e}")

    def export_student_grades_to_excel(self, student_id):
        """تصدير درجات طالب محدد إلى Excel"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبات Excel غير متوفرة\nيرجى تثبيت pandas و openpyxl")
            return

        try:
            # الحصول على درجات الطالب
            student_data = self.get_student_grades_data(student_id)
            if not student_data or not student_data['grades']:
                messagebox.showwarning("تحذير", "لا توجد درجات لهذا الطالب للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title=f"حفظ ملف Excel - درجات {student_data['student_name']}"
            )

            if not file_path:
                return

            # إنشاء DataFrame
            df = pd.DataFrame(student_data['grades'])

            # إنشاء ملف Excel مع تنسيق
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='درجات الطالب', index=False)

                # تنسيق الملف
                worksheet = writer.sheets['درجات الطالب']

                # إضافة معلومات الطالب في الأعلى
                worksheet.insert_rows(1, 3)
                worksheet['A1'] = 'اسم الطالب:'
                worksheet['B1'] = student_data['student_name']
                worksheet['A2'] = 'الرقم الجامعي:'
                worksheet['B2'] = student_data['student_id']

                # تنسيق معلومات الطالب
                info_font = Font(bold=True, size=12)
                worksheet['A1'].font = info_font
                worksheet['A2'].font = info_font
                worksheet['B1'].font = Font(size=12)
                worksheet['B2'].font = Font(size=12)

                # تنسيق الرأس (الآن في الصف 4)
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_alignment = Alignment(horizontal="center", vertical="center")

                # تطبيق التنسيق على الرأس
                for cell in worksheet[4]:
                    if cell.value:
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment

                # تنسيق البيانات
                data_alignment = Alignment(horizontal="center", vertical="center")
                thin_border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # تطبيق التنسيق على البيانات (من الصف 4 فما فوق)
                for row in worksheet.iter_rows(min_row=4):
                    for cell in row:
                        if cell.value:
                            cell.alignment = data_alignment
                            cell.border = thin_border

                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if cell.value and len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 25)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            messagebox.showinfo("نجح التصدير",
                              f"تم تصدير درجات الطالب {student_data['student_name']} بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
            print(f"خطأ في تصدير درجات Excel: {e}")

    def export_all_grades_to_excel(self):
        """تصدير جميع الدرجات إلى Excel"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبات Excel غير متوفرة\nيرجى تثبيت pandas و openpyxl")
            return

        try:
            # الحصول على جميع الدرجات
            all_grades = self.get_all_grades_data()
            if not all_grades:
                messagebox.showwarning("تحذير", "لا توجد درجات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ ملف Excel - جميع الدرجات"
            )

            if not file_path:
                return

            # إنشاء DataFrame
            df = pd.DataFrame(all_grades)

            # إنشاء ملف Excel مع تنسيق
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='جميع الدرجات', index=False)

                # تنسيق الملف
                worksheet = writer.sheets['جميع الدرجات']

                # تنسيق الرأس
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="2c3e50", end_color="2c3e50", fill_type="solid")
                header_alignment = Alignment(horizontal="center", vertical="center")

                # تطبيق التنسيق على الرأس
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment

                # تنسيق البيانات
                data_alignment = Alignment(horizontal="center", vertical="center")
                thin_border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # ألوان متناوبة للصفوف
                light_fill = PatternFill(start_color="f8f9fa", end_color="f8f9fa", fill_type="solid")

                # تطبيق التنسيق على جميع الخلايا
                for row_num, row in enumerate(worksheet.iter_rows(min_row=2), start=2):
                    for cell in row:
                        cell.alignment = data_alignment
                        cell.border = thin_border
                        # تطبيق لون متناوب
                        if row_num % 2 == 0:
                            cell.fill = light_fill

                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if cell.value and len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 25)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            messagebox.showinfo("نجح التصدير",
                              f"تم تصدير {len(all_grades)} درجة بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
            print(f"خطأ في تصدير جميع الدرجات Excel: {e}")

    def show_export_students_options(self):
        """عرض نافذة خيارات تصدير الطلاب"""
        options_window = tk.Toplevel(self.root)
        options_window.title("📊 خيارات تصدير بيانات الطلاب")
        options_window.geometry("500x600")
        options_window.configure(bg="#f5f6fa")
        options_window.resizable(False, False)

        # جعل النافذة في المقدمة
        options_window.transient(self.root)
        options_window.grab_set()

        # توسيط النافذة
        options_window.update_idletasks()
        x = (options_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (options_window.winfo_screenheight() // 2) - (600 // 2)
        options_window.geometry(f"500x600+{x}+{y}")

        # العنوان الرئيسي
        title_frame = tk.Frame(options_window, bg="#2c3e50", height=60)
        title_frame.pack(fill="x", pady=(0, 20))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="📊 اختر البيانات المراد تصديرها",
                              font=("Cairo", 16, "bold"), fg="#fff", bg="#2c3e50")
        title_label.pack(expand=True)

        # إطار الخيارات
        main_frame = tk.Frame(options_window, bg="#f5f6fa")
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # متغيرات الخيارات
        self.export_options = {
            'id': tk.BooleanVar(value=True),
            'name': tk.BooleanVar(value=True),
            'student_id': tk.BooleanVar(value=True),
            'gender': tk.BooleanVar(value=True),
            'nationality': tk.BooleanVar(value=True),
            'college': tk.BooleanVar(value=True),
            'department': tk.BooleanVar(value=True),
            'status': tk.BooleanVar(value=True),
            'level': tk.BooleanVar(value=True),
            'enrollment_date': tk.BooleanVar(value=True),
            'birth_date': tk.BooleanVar(value=True)
        }

        # قائمة الخيارات مع الأيقونات والأوصاف
        options_data = [
            ('id', '🔢', 'الرقم التسلسلي', 'رقم الطالب في النظام'),
            ('name', '👤', 'اسم الطالب', 'الاسم الكامل للطالب'),
            ('student_id', '🆔', 'الرقم الجامعي', 'رقم الطالب الجامعي'),
            ('gender', '⚥', 'الجنس', 'ذكر أو أنثى'),
            ('nationality', '🌍', 'الجنسية', 'جنسية الطالب'),
            ('college', '🏛️', 'الكلية', 'الكلية التي ينتمي إليها'),
            ('department', '📚', 'القسم', 'القسم الأكاديمي'),
            ('status', '📊', 'الحالة', 'حالة الطالب (نشط/متوقف/خريج)'),
            ('level', '📈', 'المستوى', 'المستوى الدراسي'),
            ('enrollment_date', '📅', 'تاريخ الالتحاق', 'تاريخ التحاق الطالب'),
            ('birth_date', '🎂', 'تاريخ الميلاد', 'تاريخ ميلاد الطالب')
        ]

        # إنشاء إطار قابل للتمرير
        canvas = tk.Canvas(main_frame, bg="#f5f6fa", highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f6fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إضافة الخيارات
        for i, (key, icon, title, desc) in enumerate(options_data):
            option_frame = tk.Frame(scrollable_frame, bg="#fff", relief="solid", bd=1)
            option_frame.pack(fill="x", pady=5, padx=10)

            # إطار المحتوى
            content_frame = tk.Frame(option_frame, bg="#fff")
            content_frame.pack(fill="x", padx=15, pady=10)

            # Checkbox مع الأيقونة والعنوان
            check_frame = tk.Frame(content_frame, bg="#fff")
            check_frame.pack(fill="x")

            checkbox = tk.Checkbutton(check_frame, variable=self.export_options[key],
                                    text=f"{icon} {title}", font=("Cairo", 12, "bold"),
                                    bg="#fff", fg="#2c3e50", activebackground="#fff",
                                    selectcolor="#3498db", cursor="hand2")
            checkbox.pack(side="right", anchor="e")

            # الوصف
            desc_label = tk.Label(content_frame, text=desc, font=("Cairo", 10),
                                fg="#7f8c8d", bg="#fff", anchor="w")
            desc_label.pack(fill="x", pady=(5, 0))

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # إطار الأزرار
        buttons_frame = tk.Frame(options_window, bg="#f5f6fa")
        buttons_frame.pack(fill="x", padx=20, pady=20)

        # أزرار التحكم السريع
        control_frame = tk.Frame(buttons_frame, bg="#f5f6fa")
        control_frame.pack(fill="x", pady=(0, 15))

        select_all_btn = tk.Button(control_frame, text="✅ تحديد الكل",
                                 command=lambda: self.toggle_all_options(True),
                                 font=("Cairo", 10, "bold"), bg="#27ae60", fg="#fff",
                                 padx=15, pady=5, relief="flat", cursor="hand2")
        select_all_btn.pack(side="left", padx=(0, 10))

        deselect_all_btn = tk.Button(control_frame, text="❌ إلغاء الكل",
                                   command=lambda: self.toggle_all_options(False),
                                   font=("Cairo", 10, "bold"), bg="#e74c3c", fg="#fff",
                                   padx=15, pady=5, relief="flat", cursor="hand2")
        deselect_all_btn.pack(side="left")

        # أزرار العمل الرئيسية
        action_frame = tk.Frame(buttons_frame, bg="#f5f6fa")
        action_frame.pack(fill="x")

        export_btn = tk.Button(action_frame, text="📄 تصدير إلى Word",
                             command=lambda: self.export_students_with_options(options_window),
                             font=("Cairo", 12, "bold"), bg="#2980b9", fg="#fff",
                             padx=20, pady=8, relief="flat", cursor="hand2")
        export_btn.pack(side="right", padx=(10, 0))

        cancel_btn = tk.Button(action_frame, text="❌ إلغاء",
                             command=options_window.destroy,
                             font=("Cairo", 12, "bold"), bg="#95a5a6", fg="#fff",
                             padx=20, pady=8, relief="flat", cursor="hand2")
        cancel_btn.pack(side="right")

    def toggle_all_options(self, select_all):
        """تحديد أو إلغاء تحديد جميع الخيارات"""
        for option_var in self.export_options.values():
            option_var.set(select_all)

    def export_students_with_options(self, options_window):
        """تصدير الطلاب مع الخيارات المحددة"""
        # التحقق من وجود خيارات محددة
        selected_options = [key for key, var in self.export_options.items() if var.get()]

        if not selected_options:
            messagebox.showwarning("تحذير", "يرجى تحديد حقل واحد على الأقل للتصدير")
            return

        # إغلاق نافذة الخيارات
        options_window.destroy()

        # تصدير البيانات مع الخيارات المحددة
        self.export_students_to_word(selected_options)

    def export_students_to_word(self, selected_fields=None):
        """تصدير بيانات الطلاب إلى Word مع الحقول المحددة"""
        if not WORD_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبات Word غير متوفرة\nيرجى تثبيت python-docx")
            return

        try:
            # الحصول على بيانات الطلاب
            students_data = self.get_all_students_data()
            if not students_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات طلاب للتصدير")
                return

            # إذا لم يتم تحديد حقول، استخدم جميع الحقول
            if selected_fields is None:
                selected_fields = ['id', 'name', 'student_id', 'gender', 'nationality',
                                 'college', 'department', 'status', 'level', 'enrollment_date', 'birth_date']

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".docx",
                filetypes=[("Word files", "*.docx"), ("All files", "*.*")],
                title="حفظ ملف Word - بيانات الطلاب"
            )

            if not file_path:
                return

            # إنشاء مستند Word
            doc = Document()

            # إعداد اتجاه النص من اليمين لليسار
            sections = doc.sections
            for section in sections:
                section.page_height = Inches(11.69)  # A4
                section.page_width = Inches(8.27)
                section.left_margin = Inches(0.8)
                section.right_margin = Inches(0.8)
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                # إعداد هوامش مناسبة للعربية
                pass  # تم تبسيط الكود لتجنب الأخطاء

            # إضافة العنوان مع إصلاح انعكاس الكلمات
            title = doc.add_heading('', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # الحصول على الفقرة
            paragraph = title.paragraphs[0] if hasattr(title, 'paragraphs') and title.paragraphs else title

            # استخدام الدالة المحسنة لإصلاح انعكاس الكلمات
            run = self.set_rtl_paragraph(paragraph, 'تقرير بيانات الطلاب')
            if run:
                run.font.size = Pt(16)
                run.font.bold = True

            # إضافة فقرة فارغة
            doc.add_paragraph()

            # تحديد الرؤوس والبيانات بناءً على الحقول المحددة
            field_mapping = {
                'id': ('الرقم', 'الرقم'),
                'name': ('الاسم', 'الاسم'),
                'student_id': ('الرقم الجامعي', 'الرقم الجامعي'),
                'gender': ('الجنس', 'الجنس'),
                'nationality': ('الجنسية', 'الجنسية'),
                'college': ('الكلية', 'الكلية'),
                'department': ('القسم', 'القسم'),
                'status': ('الحالة', 'الحالة'),
                'level': ('المستوى', 'المستوى'),
                'enrollment_date': ('تاريخ الالتحاق', 'تاريخ التسجيل'),
                'birth_date': ('تاريخ الميلاد', 'تاريخ الميلاد')
            }

            # إنشاء رؤوس الجدول بناءً على الحقول المحددة (من اليمين لليسار)
            headers = []
            data_keys = []
            for field in reversed(selected_fields):  # عكس الترتيب للحصول على اتجاه من اليمين لليسار
                if field in field_mapping:
                    headers.append(field_mapping[field][0])
                    data_keys.append(field_mapping[field][1])

            # إنشاء الجدول
            table = doc.add_table(rows=1, cols=len(headers))
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.CENTER

            # إضافة رأس الجدول مع إصلاح انعكاس النصوص
            hdr_cells = table.rows[0].cells
            for i, header in enumerate(headers):
                # تطبيق إصلاح انعكاس النصوص على الخلية مع إضافة النص
                run = self.set_rtl_table_cell(hdr_cells[i], header)

                # تعديل التنسيق إذا تم إنشاء الـ run بنجاح
                if run:
                    run.bold = True
                    run.font.size = Pt(11)

            # إضافة بيانات الطلاب مع إصلاح انعكاس النصوص
            for student in students_data:
                row_cells = table.add_row().cells

                for i, data_key in enumerate(data_keys):
                    data_value = student.get(data_key, '')
                    cell_text = str(data_value) if data_value else ''

                    # تطبيق إصلاح انعكاس النصوص على الخلية مع إضافة النص
                    run = self.set_rtl_table_cell(row_cells[i], cell_text)

                    # تعديل حجم الخط إذا تم إنشاء الـ run بنجاح
                    if run:
                        run.font.size = Pt(10)

            # إضافة معلومات إضافية مع إصلاح انعكاس النصوص
            doc.add_paragraph()
            info_para = doc.add_paragraph()

            # استخدام الدالة المحسنة لإصلاح انعكاس الكلمات
            info_run = self.set_rtl_paragraph(info_para, f"إجمالي عدد الطلاب: {len(students_data)}")
            if info_run:
                info_run.bold = True
                info_run.font.size = Pt(12)

            # إضافة تاريخ التصدير مع إصلاح انعكاس النصوص
            from datetime import datetime
            export_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            date_para = doc.add_paragraph()

            # استخدام الدالة المحسنة لإصلاح انعكاس الكلمات
            date_run = self.set_rtl_paragraph(date_para, f"تاريخ التصدير: {export_date}")
            if date_run:
                date_run.font.size = Pt(10)

            # حفظ المستند
            doc.save(file_path)

            messagebox.showinfo("نجح التصدير", f"تم تصدير بيانات {len(students_data)} طالب بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
            print(f"خطأ في تصدير Word: {e}")

    def export_student_grades_to_word(self, student_id):
        """تصدير درجات طالب محدد إلى Word"""
        if not WORD_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبات Word غير متوفرة\nيرجى تثبيت python-docx")
            return

        try:
            # الحصول على درجات الطالب
            student_data = self.get_student_grades_data(student_id)
            if not student_data or not student_data['grades']:
                messagebox.showwarning("تحذير", "لا توجد درجات لهذا الطالب للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".docx",
                filetypes=[("Word files", "*.docx"), ("All files", "*.*")],
                title=f"حفظ ملف Word - درجات {student_data['student_name']}"
            )

            if not file_path:
                return

            # إنشاء مستند Word
            doc = Document()

            # إعداد الصفحة
            sections = doc.sections
            for section in sections:
                section.page_height = Inches(11.69)  # A4
                section.page_width = Inches(8.27)
                section.left_margin = Inches(1)
                section.right_margin = Inches(1)
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                # إعداد هوامش مناسبة للعربية
                pass  # تم تبسيط الكود

            # إضافة العنوان مع إصلاح انعكاس الكلمات
            title = doc.add_heading('', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # الحصول على الفقرة
            paragraph = title.paragraphs[0] if hasattr(title, 'paragraphs') and title.paragraphs else title

            # استخدام الدالة المحسنة لإصلاح انعكاس الكلمات
            run = self.set_rtl_paragraph(paragraph, f'درجات الطالب: {student_data["student_name"]}')
            if run:
                run.font.size = Pt(16)
                run.font.bold = True

            # إضافة معلومات الطالب مع إصلاح انعكاس الكلمات
            info_para = doc.add_paragraph()

            # استخدام الدالة المحسنة لإصلاح انعكاس الكلمات
            info_run = self.set_rtl_paragraph(info_para, f"الرقم الجامعي: {student_data['student_id']}")
            if info_run:
                info_run.bold = True
                info_run.font.size = Pt(12)

            # إضافة فقرة فارغة
            doc.add_paragraph()

            # إنشاء الجدول
            headers = ['تاريخ الإدخال', 'التقدير', 'المجموع', 'درجة النهائي', 'درجة الجزئي', 'الفصل الدراسي']
            table = doc.add_table(rows=1, cols=len(headers))
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.CENTER

            # إضافة رأس الجدول
            hdr_cells = table.rows[0].cells
            for i, header in enumerate(headers):
                hdr_cells[i].text = header
                # تنسيق رأس الجدول
                for paragraph in hdr_cells[i].paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in paragraph.runs:
                        run.bold = True
                        run.font.size = Pt(11)

            # إضافة درجات الطالب
            for grade in student_data['grades']:
                row_cells = table.add_row().cells
                row_data = [
                    grade['تاريخ الإدخال'][:10] if grade['تاريخ الإدخال'] else '',
                    grade['التقدير'],
                    str(grade['المجموع']),
                    str(grade['درجة النهائي']),
                    str(grade['درجة الجزئي']),
                    grade['الفصل الدراسي']
                ]

                for i, data in enumerate(row_data):
                    row_cells[i].text = str(data) if data else ''
                    # تنسيق الخلايا
                    self.set_rtl_table_cell(row_cells[i])
                    for paragraph in row_cells[i].paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in paragraph.runs:
                            run.font.size = Pt(10)

            # إضافة معلومات إضافية
            doc.add_paragraph()

            # حساب المعدل إذا كان هناك درجات
            if student_data['grades']:
                total_grades = sum(float(grade['المجموع']) for grade in student_data['grades'])
                average = total_grades / len(student_data['grades'])

                summary_para = doc.add_paragraph()
                summary_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # عرض المعدل العام فقط
                summary_run = summary_para.add_run(f"المعدل العام: {average:.2f}")
                summary_run.bold = True
                summary_run.font.size = Pt(12)

            # إضافة تاريخ التصدير
            from datetime import datetime
            export_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            date_para = doc.add_paragraph()
            date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            date_run = date_para.add_run(f"تاريخ التصدير: {export_date}")
            date_run.font.size = Pt(10)

            # حفظ المستند
            doc.save(file_path)

            messagebox.showinfo("نجح التصدير",
                              f"تم تصدير درجات الطالب {student_data['student_name']} بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
            print(f"خطأ في تصدير درجات Word: {e}")

    def export_all_grades_to_word(self):
        """تصدير جميع الدرجات إلى Word"""
        if not WORD_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبات Word غير متوفرة\nيرجى تثبيت python-docx")
            return

        try:
            # الحصول على جميع الدرجات
            all_grades = self.get_all_grades_data()
            if not all_grades:
                messagebox.showwarning("تحذير", "لا توجد درجات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                defaultextension=".docx",
                filetypes=[("Word files", "*.docx"), ("All files", "*.*")],
                title="حفظ ملف Word - جميع الدرجات"
            )

            if not file_path:
                return

            # إنشاء مستند Word
            doc = Document()

            # إعداد الصفحة
            sections = doc.sections
            for section in sections:
                section.page_height = Inches(11.69)  # A4
                section.page_width = Inches(8.27)
                section.left_margin = Inches(0.8)
                section.right_margin = Inches(0.8)
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                # إعداد هوامش مناسبة للعربية
                pass  # تم تبسيط الكود

            # إضافة العنوان
            title = doc.add_heading('تقرير جميع الدرجات', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            # العنوان بتنسيق مناسب للعربية
            pass  # تم تبسيط الكود

            # إضافة فقرة فارغة
            doc.add_paragraph()

            # إنشاء الجدول
            headers = ['التقدير', 'المجموع', 'النهائي', 'الجزئي', 'الفصل', 'المستوى', 'القسم', 'الكلية', 'الرقم الجامعي', 'اسم الطالب']
            table = doc.add_table(rows=1, cols=len(headers))
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.CENTER

            # إضافة رأس الجدول
            hdr_cells = table.rows[0].cells
            for i, header in enumerate(headers):
                hdr_cells[i].text = header
                # تنسيق رأس الجدول
                for paragraph in hdr_cells[i].paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in paragraph.runs:
                        run.bold = True
                        run.font.size = Pt(10)

            # إضافة جميع الدرجات
            for grade in all_grades:
                row_cells = table.add_row().cells
                row_data = [
                    grade['التقدير'],
                    str(grade['المجموع']),
                    str(grade['درجة النهائي']),
                    str(grade['درجة الجزئي']),
                    grade['الفصل الدراسي'],
                    grade['المستوى'],
                    grade['القسم'],
                    grade['الكلية'],
                    grade['الرقم الجامعي'],
                    grade['اسم الطالب']
                ]

                for i, data in enumerate(row_data):
                    row_cells[i].text = str(data) if data else ''
                    # تنسيق الخلايا
                    for paragraph in row_cells[i].paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in paragraph.runs:
                            run.font.size = Pt(9)

            # إضافة إحصائيات
            doc.add_paragraph()

            # حساب إحصائيات
            total_students = len(set(grade['اسم الطالب'] for grade in all_grades))
            total_grades = len(all_grades)
            avg_grade = sum(float(grade['المجموع']) for grade in all_grades) / total_grades if total_grades > 0 else 0

            stats_para = doc.add_paragraph()
            stats_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            stats_run = stats_para.add_run(f"إجمالي الطلاب: {total_students} | إجمالي الدرجات: {total_grades} | المعدل العام: {avg_grade:.2f}")
            stats_run.bold = True
            stats_run.font.size = Pt(12)

            # إضافة تاريخ التصدير
            from datetime import datetime
            export_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            date_para = doc.add_paragraph()
            date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            date_run = date_para.add_run(f"تاريخ التصدير: {export_date}")
            date_run.font.size = Pt(10)

            # حفظ المستند
            doc.save(file_path)

            messagebox.showinfo("نجح التصدير", f"تم تصدير {len(all_grades)} درجة بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
            print(f"خطأ في تصدير جميع الدرجات Word: {e}")

if __name__ == "__main__":
    try:
        # بدء نظام تسجيل الدخول
        login_root = tk.Tk()
        login_app = LoginSystem(login_root)
        login_root.mainloop()
    except Exception as e:
        import traceback
        print("[Startup Error]:", e)
        traceback.print_exc()
        messagebox.showerror("خطأ فادح", f"حدث خطأ عند بدء البرنامج:\n{e}")